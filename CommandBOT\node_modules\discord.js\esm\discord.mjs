import Discord from '../src/index.js';

export default Discord;

export const {
  BaseClient,
  Client,
  Shard,
  ShardClientUtil,
  ShardingManager,
  WebhookClient,
  ActivityFlags,
  BitField,
  Collection,
  Constants,
  DataResolver,
  BaseManager,
  DiscordAPIError,
  HTTPError,
  MessageFlags,
  Intents,
  Permissions,
  Speaking,
  Snowflake,
  SnowflakeUtil,
  Structures,
  SystemChannelFlags,
  UserFlags,
  Util,
  version,
  ChannelManager,
  GuildChannelManager,
  GuildEmojiManager,
  GuildEmojiRoleManager,
  GuildMemberManager,
  GuildMemberRoleManager,
  GuildManager,
  ReactionManager,
  ReactionUserManager,
  MessageManager,
  PresenceManager,
  RoleManager,
  UserManager,
  discordSort,
  escapeMarkdown,
  fetchRecommendedShards,
  resolveColor,
  resolveString,
  splitMessage,
  Application,
  Base,
  Activity,
  APIMessage,
  BaseGuildEmoji,
  CategoryChannel,
  Channel,
  ClientApplication,
  ClientUser,
  Collector,
  DMChannel,
  Emoji,
  Guild,
  GuildAuditLogs,
  GuildChannel,
  GuildEmoji,
  GuildMember,
  GuildPreview,
  GuildTemplate,
  Integration,
  Invite,
  Message,
  MessageAttachment,
  MessageCollector,
  MessageEmbed,
  MessageMentions,
  MessageReaction,
  NewsChannel,
  PermissionOverwrites,
  Presence,
  ClientPresence,
  ReactionCollector,
  ReactionEmoji,
  RichPresenceAssets,
  Role,
  StoreChannel,
  Team,
  TeamMember,
  TextChannel,
  User,
  VoiceChannel,
  VoiceRegion,
  VoiceState,
  Webhook,
  WebSocket
} = Discord;
