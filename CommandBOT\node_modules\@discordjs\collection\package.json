{"name": "@discordjs/collection", "version": "0.1.6", "description": "Utility data structure used in Discord.js", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"lint": "eslint src --ext .ts", "prebuild": "npm run lint", "build": "rimraf dist/ && tsc", "pretest": "npm run build", "test": "node test/index.js", "docs": "docgen --jsdoc jsdoc.json --source src/*.ts src/**/*.ts --custom docs/index.yml --output docs/docs.json", "docs:test": "docgen --jsdoc jsdoc.json --source src/*.ts src/**/*.ts --custom docs/index.yml"}, "repository": {"type": "git", "url": "git+https://github.com/discordjs/collection.git"}, "keywords": ["map", "collection", "utility"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "Apache-2.0", "bugs": {"url": "https://github.com/discordjs/collection/issues"}, "homepage": "https://github.com/discordjs/collection#readme", "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.8.4", "@babel/preset-env": "^7.8.4", "@babel/preset-typescript": "^7.8.3", "@types/node": "^13.7.4", "@typescript-eslint/eslint-plugin": "^2.21.0", "@typescript-eslint/parser": "^2.21.0", "discord.js-docgen": "discordjs/docgen#ts-patch", "eslint": "^6.8.0", "eslint-config-marine": "^6.0.0", "jsdoc-babel": "^0.5.0", "rimraf": "^3.0.2", "typescript": "^3.8.2"}, "eslintConfig": {"extends": "marine/node"}}