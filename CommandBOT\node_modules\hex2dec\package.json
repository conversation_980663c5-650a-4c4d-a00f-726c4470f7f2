{"name": "hex2dec", "version": "1.1.2", "description": "Arbitrary precision decimal/hexadecimal converter.", "main": "index.js", "scripts": {"test": "mocha tests/*.test.js", "preversion": "npm test", "postversion": "git push && git push --tags && npm publish"}, "repository": {"type": "git", "url": "git+https://github.com/donmccurdy/hex2dec.git"}, "keywords": ["decimal", "hexadecimal", "converter", "precision", "int64", "long", "hex"], "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "license": "Apache-2.0", "bugs": {"url": "https://github.com/donmccurdy/hex2dec/issues"}, "homepage": "https://github.com/donmccurdy/hex2dec#readme", "devDependencies": {"chai": "^3.5.0", "mocha": "^2.5.3"}}