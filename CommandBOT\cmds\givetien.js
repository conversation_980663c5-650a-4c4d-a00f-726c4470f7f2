exports.permission = {
    channelid  : ["1372175124052181114"],
    roleid : ["1127380275865845940", "1326971562779218001", "1329313541177479189"]
}

exports.help = "[ID] [Số lượng]"

exports.run = async (message, args) => {
    try {
        let playerId = args[1]
        if (!playerId) return message.reply("`Vui lòng nhập ID`")     

        let amount = args[2]
        if (!amount) return message.reply("`Vui lòng nhập số lượng`")     


        let data = await fetch(`${host}/givetien`, {
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                data : {
                    'id' :playerId,
                    'amount' : amount
                }
            }),
            method : "POST",
        }).then(data => data.json())
        if (data.error) {
            vralitylog.error("Respone " + path.basename(__filename), data.error)
            message.reply("`<PERSON><PERSON> lỗi xảy ra`")
            message.reply('```fix\n' + data.error + "```")
            return
        }
        message.reply(data.success + func.getTimeString())
    }
    catch (err) { 
        vralitylog.error(path.basename(__filename), err)
        connected = false;
    }
}

    
    

    
    