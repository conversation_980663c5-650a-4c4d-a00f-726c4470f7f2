-- Client-side script để xử lý teleport từ Discord bot

print("DiscordBOT Client: Script đã được load!")

-- Function để thực hiện teleport
local function DoTeleport(coords, source)
    print("DiscordBOT Client: Bắt đầu teleport từ", source or "unknown")
    print("DiscordBOT Client: Coords nhận được:", json.encode(coords))

    local playerPed = PlayerPedId()
    print("DiscordBOT Client: Player Ped ID:", playerPed)

    if coords and coords.x and coords.y and coords.z then
        print("DiscordBOT Client: Đang teleport đến:", coords.x, coords.y, coords.z)

        -- Lưu vị trí cũ để so sánh
        local oldCoords = GetEntityCoords(playerPed)
        print("DiscordBOT Client: Vị trí hiện tại:", oldCoords.x, oldCoords.y, oldCoords.z)

        -- Teleport người chơi đến tọa độ với nhiều cách kh<PERSON><PERSON> nhau
        SetEntityCoords(playerPed, coords.x, coords.y, coords.z, false, false, false, true)

        -- Đảm bảo player không bị stuck
        SetEntityHeading(playerPed, 0.0)
        FreezeEntityPosition(playerPed, false)

        -- Đợi một chút rồi kiểm tra vị trí mới
        Citizen.Wait(500)
        local newCoords = GetEntityCoords(playerPed)
        print("DiscordBOT Client: Vị trí sau teleport:", newCoords.x, newCoords.y, newCoords.z)

        -- Kiểm tra xem có teleport thành công không
        local distance = #(vector3(newCoords.x, newCoords.y, newCoords.z) - vector3(coords.x, coords.y, coords.z))
        if distance < 5.0 then
            print("DiscordBOT Client: Teleport thành công! Distance:", distance)

            -- Thông báo cho người chơi
            SetNotificationTextEntry("STRING")
            AddTextComponentString("~g~Bạn đã được teleport bởi Admin!")
            DrawNotification(false, false)

            -- Gửi message trong chat
            TriggerEvent('chat:addMessage', {
                color = {0, 255, 0},
                multiline = true,
                args = {"[ADMIN]", "Bạn đã được teleport thành công!"}
            })
        else
            print("DiscordBOT Client: Teleport thất bại! Distance:", distance)
            -- Thử lại lần nữa
            SetEntityCoords(playerPed, coords.x, coords.y, coords.z + 1.0, false, false, false, true)
        end

        print("DiscordBOT Client: Teleport hoàn thành!")
    else
        print("DiscordBOT Client: Lỗi - Tọa độ teleport không hợp lệ")
        print("DiscordBOT Client: Coords:", coords)
    end
end

-- Event handler chính
RegisterNetEvent('bringAmbulanceCl')
AddEventHandler('bringAmbulanceCl', function(coords)
    print("DiscordBOT Client: Nhận được event bringAmbulanceCl")
    DoTeleport(coords, "bringAmbulanceCl")
end)

-- Thêm event handler backup
RegisterNetEvent('discord:teleport')
AddEventHandler('discord:teleport', function(coords)
    print("DiscordBOT Client: Nhận được event discord:teleport")
    DoTeleport(coords, "discord:teleport")
end)

-- Event handler cho ESX
RegisterNetEvent('esx:teleportPlayer')
AddEventHandler('esx:teleportPlayer', function(coords)
    print("DiscordBOT Client: Nhận được event esx:teleportPlayer")
    DoTeleport(coords, "esx:teleportPlayer")
end)

-- Event handler cho teleport đến vị trí cụ thể khác (nếu cần)
RegisterNetEvent('discord:teleportPlayer')
AddEventHandler('discord:teleportPlayer', function(x, y, z)
    local playerPed = PlayerPedId()
    
    if x and y and z then
        SetEntityCoords(playerPed, x, y, z, false, false, false, true)
        
        SetNotificationTextEntry("STRING")
        AddTextComponentString("~g~Teleport thành công!")
        DrawNotification(false, false)
        
        print("DiscordBOT: Teleport đến: " .. x .. ", " .. y .. ", " .. z)
    end
end)

-- Event handler cho teleport đến người chơi khác
RegisterNetEvent('discord:teleportToPlayer')
AddEventHandler('discord:teleportToPlayer', function(targetPlayerId)
    local targetPed = GetPlayerPed(GetPlayerFromServerId(targetPlayerId))
    
    if DoesEntityExist(targetPed) then
        local coords = GetEntityCoords(targetPed)
        local playerPed = PlayerPedId()
        
        SetEntityCoords(playerPed, coords.x, coords.y, coords.z, false, false, false, true)
        
        SetNotificationTextEntry("STRING")
        AddTextComponentString("~g~Đã teleport đến người chơi!")
        DrawNotification(false, false)
    else
        SetNotificationTextEntry("STRING")
        AddTextComponentString("~r~Không thể tìm thấy người chơi!")
        DrawNotification(false, false)
    end
end)
