-- Client-side script để xử lý teleport từ Discord bot

RegisterNetEvent('bringAmbulanceCl')
AddEventHandler('bringAmbulanceCl', function(coords)
    local playerPed = PlayerPedId()
    
    -- <PERSON><PERSON><PERSON> tra nếu coords c<PERSON> đ<PERSON>y đủ thông tin
    if coords and coords.x and coords.y and coords.z then
        -- Teleport người chơi đến tọa độ
        SetEntityCoords(playerPed, coords.x, coords.y, coords.z, false, false, false, true)
        
        -- Thông báo cho người chơi
        SetNotificationTextEntry("STRING")
        AddTextComponentString("~g~Bạn đã được teleport bởi Admin!")
        DrawNotification(false, false)
        
        -- Log để debug
        print("DiscordBOT: Đã teleport đến tọa độ: " .. coords.x .. ", " .. coords.y .. ", " .. coords.z)
    else
        print("DiscordBOT: Lỗi - T<PERSON><PERSON> độ teleport không hợp lệ")
    end
end)

-- Event handler cho teleport đến vị trí cụ thể khác (nếu cần)
RegisterNetEvent('discord:teleportPlayer')
AddEventHandler('discord:teleportPlayer', function(x, y, z)
    local playerPed = PlayerPedId()
    
    if x and y and z then
        SetEntityCoords(playerPed, x, y, z, false, false, false, true)
        
        SetNotificationTextEntry("STRING")
        AddTextComponentString("~g~Teleport thành công!")
        DrawNotification(false, false)
        
        print("DiscordBOT: Teleport đến: " .. x .. ", " .. y .. ", " .. z)
    end
end)

-- Event handler cho teleport đến người chơi khác
RegisterNetEvent('discord:teleportToPlayer')
AddEventHandler('discord:teleportToPlayer', function(targetPlayerId)
    local targetPed = GetPlayerPed(GetPlayerFromServerId(targetPlayerId))
    
    if DoesEntityExist(targetPed) then
        local coords = GetEntityCoords(targetPed)
        local playerPed = PlayerPedId()
        
        SetEntityCoords(playerPed, coords.x, coords.y, coords.z, false, false, false, true)
        
        SetNotificationTextEntry("STRING")
        AddTextComponentString("~g~Đã teleport đến người chơi!")
        DrawNotification(false, false)
    else
        SetNotificationTextEntry("STRING")
        AddTextComponentString("~r~Không thể tìm thấy người chơi!")
        DrawNotification(false, false)
    end
end)
