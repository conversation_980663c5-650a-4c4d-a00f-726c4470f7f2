-- Client-side script để xử lý teleport từ Discord bot

print("DiscordBOT Client: Script đã được load!")

RegisterNetEvent('bringAmbulanceCl')
AddEventHandler('bringAmbulanceCl', function(coords)
    print("DiscordBOT Client: Nhận được event bringAmbulanceCl")
    print("DiscordBOT Client: Coords nhận được:", json.encode(coords))

    local playerPed = PlayerPedId()
    print("DiscordBOT Client: Player Ped ID:", playerPed)

    -- <PERSON>ể<PERSON> tra nếu coords có đầy đủ thông tin
    if coords and coords.x and coords.y and coords.z then
        print("DiscordBOT Client: Đang teleport đến:", coords.x, coords.y, coords.z)

        -- Teleport người chơi đến tọa độ
        SetEntityCoords(playerPed, coords.x, coords.y, coords.z, false, false, false, true)

        -- Đợ<PERSON> một chút rồi kiểm tra vị trí mới
        Citizen.Wait(100)
        local newCoords = GetEntityCoords(playerPed)
        print("DiscordBOT Client: Vị trí sau teleport:", newCoords.x, newCoords.y, newCoords.z)

        -- Thông báo cho người chơi
        SetNotificationTextEntry("STRING")
        AddTextComponentString("~g~Bạn đã được teleport bởi Admin!")
        DrawNotification(false, false)

        -- Gửi message trong chat
        TriggerEvent('chat:addMessage', {
            color = {0, 255, 0},
            multiline = true,
            args = {"[ADMIN]", "Bạn đã được teleport!"}
        })

        print("DiscordBOT Client: Teleport hoàn thành!")
    else
        print("DiscordBOT Client: Lỗi - Tọa độ teleport không hợp lệ")
        print("DiscordBOT Client: Coords:", coords)
    end
end)

-- Event handler cho teleport đến vị trí cụ thể khác (nếu cần)
RegisterNetEvent('discord:teleportPlayer')
AddEventHandler('discord:teleportPlayer', function(x, y, z)
    local playerPed = PlayerPedId()
    
    if x and y and z then
        SetEntityCoords(playerPed, x, y, z, false, false, false, true)
        
        SetNotificationTextEntry("STRING")
        AddTextComponentString("~g~Teleport thành công!")
        DrawNotification(false, false)
        
        print("DiscordBOT: Teleport đến: " .. x .. ", " .. y .. ", " .. z)
    end
end)

-- Event handler cho teleport đến người chơi khác
RegisterNetEvent('discord:teleportToPlayer')
AddEventHandler('discord:teleportToPlayer', function(targetPlayerId)
    local targetPed = GetPlayerPed(GetPlayerFromServerId(targetPlayerId))
    
    if DoesEntityExist(targetPed) then
        local coords = GetEntityCoords(targetPed)
        local playerPed = PlayerPedId()
        
        SetEntityCoords(playerPed, coords.x, coords.y, coords.z, false, false, false, true)
        
        SetNotificationTextEntry("STRING")
        AddTextComponentString("~g~Đã teleport đến người chơi!")
        DrawNotification(false, false)
    else
        SetNotificationTextEntry("STRING")
        AddTextComponentString("~r~Không thể tìm thấy người chơi!")
        DrawNotification(false, false)
    end
end)
