{"name": "clear-require", "version": "3.0.0", "description": "Clear a module from the `require` cache", "license": "MIT", "repository": "sindresorhus/clear-require", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["clear", "require", "cache", "uncache", "uncached", "module", "unrequire", "derequire", "delete", "del", "remove", "rm"], "dependencies": {"caller-path": "^2.0.0", "resolve-from": "^2.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}}