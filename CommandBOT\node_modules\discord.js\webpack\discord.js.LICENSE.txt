/*! no static exports found */

/*!********************!*\
  !*** ws (ignored) ***!
  \********************/

/*!**********************!*\
  !*** ./package.json ***!
  \**********************/

/*!**********************!*\
  !*** ./src/index.js ***!
  \**********************/

/*!**********************!*\
  !*** util (ignored) ***!
  \**********************/

/*!***********************!*\
  !*** https (ignored) ***!
  \***********************/

/*!*************************!*\
  !*** erlpack (ignored) ***!
  \*************************/

/*!**************************!*\
  !*** ./src/WebSocket.js ***!
  \**************************/

/*!**************************!*\
  !*** ./src/util/Util.js ***!
  \**************************/

/*!***************************!*\
  !*** zlib-sync (ignored) ***!
  \***************************/

/*!*****************************!*\
  !*** ./src/errors/index.js ***!
  \*****************************/

/*!*****************************!*\
  !*** ./src/util/Intents.js ***!
  \*****************************/

/*!******************************!*\
  !*** ./src/client/Client.js ***!
  \******************************/

/*!******************************!*\
  !*** ./src/util/BitField.js ***!
  \******************************/

/*!******************************!*\
  !*** ./src/util/Speaking.js ***!
  \******************************/

/*!*******************************!*\
  !*** ./src/rest/APIRouter.js ***!
  \*******************************/

/*!*******************************!*\
  !*** ./src/rest/HTTPError.js ***!
  \*******************************/

/*!*******************************!*\
  !*** ./src/util/Constants.js ***!
  \*******************************/

/*!*******************************!*\
  !*** ./src/util/Snowflake.js ***!
  \*******************************/

/*!*******************************!*\
  !*** ./src/util/UserFlags.js ***!
  \*******************************/

/*!********************************!*\
  !*** ./src/errors/DJSError.js ***!
  \********************************/

/*!********************************!*\
  !*** ./src/errors/Messages.js ***!
  \********************************/

/*!********************************!*\
  !*** ./src/rest/APIRequest.js ***!
  \********************************/

/*!********************************!*\
  !*** ./src/rest/AsyncQueue.js ***!
  \********************************/

/*!********************************!*\
  !*** ./src/structures/Base.js ***!
  \********************************/

/*!********************************!*\
  !*** ./src/structures/Role.js ***!
  \********************************/

/*!********************************!*\
  !*** ./src/structures/Team.js ***!
  \********************************/

/*!********************************!*\
  !*** ./src/structures/User.js ***!
  \********************************/

/*!********************************!*\
  !*** ./src/util/Collection.js ***!
  \********************************/

/*!********************************!*\
  !*** ./src/util/Structures.js ***!
  \********************************/

/*!********************************!*\
  !*** worker_threads (ignored) ***!
  \********************************/

/*!*********************************!*\
  !*** ./src/rest/RESTManager.js ***!
  \*********************************/

/*!*********************************!*\
  !*** ./src/structures/Emoji.js ***!
  \*********************************/

/*!*********************************!*\
  !*** ./src/structures/Guild.js ***!
  \*********************************/

/*!*********************************!*\
  !*** ./src/util/Permissions.js ***!
  \*********************************/

/*!**********************************!*\
  !*** ./sharding/Shard (ignored) ***!
  \**********************************/

/*!**********************************!*\
  !*** ./src/client/BaseClient.js ***!
  \**********************************/

/*!**********************************!*\
  !*** ./src/structures/Invite.js ***!
  \**********************************/

/*!**********************************!*\
  !*** ./src/util/DataResolver.js ***!
  \**********************************/

/*!**********************************!*\
  !*** ./src/util/MessageFlags.js ***!
  \**********************************/

/*!***********************************!*\
  !*** (webpack)/buildin/global.js ***!
  \***********************************/

/*!***********************************!*\
  !*** ./node_modules/util/util.js ***!
  \***********************************/

/*!***********************************!*\
  !*** ./src/structures/Channel.js ***!
  \***********************************/

/*!***********************************!*\
  !*** ./src/structures/Message.js ***!
  \***********************************/

/*!***********************************!*\
  !*** ./src/structures/Webhook.js ***!
  \***********************************/

/*!***********************************!*\
  !*** ./src/util/ActivityFlags.js ***!
  \***********************************/

/*!************************************!*\
  !*** ./src/rest/RequestHandler.js ***!
  \************************************/

/*!************************************!*\
  !*** ./src/structures/Presence.js ***!
  \************************************/

/*!*************************************!*\
  !*** ./src/client/WebhookClient.js ***!
  \*************************************/

/*!*************************************!*\
  !*** ./src/managers/BaseManager.js ***!
  \*************************************/

/*!*************************************!*\
  !*** ./src/managers/RoleManager.js ***!
  \*************************************/

/*!*************************************!*\
  !*** ./src/managers/UserManager.js ***!
  \*************************************/

/*!*************************************!*\
  !*** ./src/rest/DiscordAPIError.js ***!
  \*************************************/

/*!*************************************!*\
  !*** ./src/structures/DMChannel.js ***!
  \*************************************/

/*!**************************************!*\
  !*** ./node_modules/buffer/index.js ***!
  \**************************************/

/*!**************************************!*\
  !*** ./src/client/actions/Action.js ***!
  \**************************************/

/*!**************************************!*\
  !*** ./src/managers/GuildManager.js ***!
  \**************************************/

/*!**************************************!*\
  !*** ./src/structures/APIMessage.js ***!
  \**************************************/

/*!**************************************!*\
  !*** ./src/structures/ClientUser.js ***!
  \**************************************/

/*!**************************************!*\
  !*** ./src/structures/GuildEmoji.js ***!
  \**************************************/

/*!**************************************!*\
  !*** ./src/structures/TeamMember.js ***!
  \**************************************/

/*!**************************************!*\
  !*** ./src/structures/VoiceState.js ***!
  \**************************************/

/*!***************************************!*\
  !*** ./node_modules/events/events.js ***!
  \***************************************/

/*!***************************************!*\
  !*** ./node_modules/ieee754/index.js ***!
  \***************************************/

/*!***************************************!*\
  !*** ./node_modules/isarray/index.js ***!
  \***************************************/

/*!***************************************!*\
  !*** ./src/structures/GuildMember.js ***!
  \***************************************/

/*!***************************************!*\
  !*** ./src/structures/Integration.js ***!
  \***************************************/

/*!***************************************!*\
  !*** ./src/structures/NewsChannel.js ***!
  \***************************************/

/*!***************************************!*\
  !*** ./src/structures/TextChannel.js ***!
  \***************************************/

/*!***************************************!*\
  !*** ./src/structures/VoiceRegion.js ***!
  \***************************************/

/*!***************************************!*\
  !*** ./src/util/LimitedCollection.js ***!
  \***************************************/

/*!****************************************!*\
  !*** ./src/managers/ChannelManager.js ***!
  \****************************************/

/*!****************************************!*\
  !*** ./src/managers/MessageManager.js ***!
  \****************************************/

/*!****************************************!*\
  !*** ./src/structures/GuildChannel.js ***!
  \****************************************/

/*!****************************************!*\
  !*** ./src/structures/GuildPreview.js ***!
  \****************************************/

/*!****************************************!*\
  !*** ./src/structures/MessageEmbed.js ***!
  \****************************************/

/*!****************************************!*\
  !*** ./src/structures/StoreChannel.js ***!
  \****************************************/

/*!****************************************!*\
  !*** ./src/structures/VoiceChannel.js ***!
  \****************************************/

/*!****************************************!*\
  !*** ./src/util/SystemChannelFlags.js ***!
  \****************************************/

/*!*****************************************!*\
  !*** ./node_modules/base64-js/index.js ***!
  \*****************************************/

/*!*****************************************!*\
  !*** ./node_modules/process/browser.js ***!
  \*****************************************/

/*!*****************************************!*\
  !*** ./src/managers/PresenceManager.js ***!
  \*****************************************/

/*!*****************************************!*\
  !*** ./src/managers/ReactionManager.js ***!
  \*****************************************/

/*!*****************************************!*\
  !*** ./src/structures/GuildTemplate.js ***!
  \*****************************************/

/*!*****************************************!*\
  !*** ./src/structures/ReactionEmoji.js ***!
  \*****************************************/

/*!******************************************!*\
  !*** ./src/client/actions/UserUpdate.js ***!
  \******************************************/

/*!******************************************!*\
  !*** ./src/structures/BaseGuildEmoji.js ***!
  \******************************************/

/*!******************************************!*\
  !*** ./src/structures/ClientPresence.js ***!
  \******************************************/

/*!******************************************!*\
  !*** ./src/structures/GuildAuditLogs.js ***!
  \******************************************/

/*!*******************************************!*\
  !*** ./node_modules/safe-buffer/index.js ***!
  \*******************************************/

/*!*******************************************!*\
  !*** ./src/client/actions/GuildDelete.js ***!
  \*******************************************/

/*!*******************************************!*\
  !*** ./src/client/actions/GuildUpdate.js ***!
  \*******************************************/

/*!*******************************************!*\
  !*** ./src/client/actions/TypingStart.js ***!
  \*******************************************/

/*!*******************************************!*\
  !*** ./src/managers/GuildEmojiManager.js ***!
  \*******************************************/

/*!*******************************************!*\
  !*** ./src/managers/VoiceStateManager.js ***!
  \*******************************************/

/*!*******************************************!*\
  !*** ./src/structures/CategoryChannel.js ***!
  \*******************************************/

/*!*******************************************!*\
  !*** ./src/structures/MessageMentions.js ***!
  \*******************************************/

/*!*******************************************!*\
  !*** ./src/structures/MessageReaction.js ***!
  \*******************************************/

/*!********************************************!*\
  !*** ./node_modules/node-fetch/browser.js ***!
  \********************************************/

/*!********************************************!*\
  !*** ./sharding/ShardClientUtil (ignored) ***!
  \********************************************/

/*!********************************************!*\
  !*** ./sharding/ShardingManager (ignored) ***!
  \********************************************/

/*!********************************************!*\
  !*** ./src/client/actions/InviteCreate.js ***!
  \********************************************/

/*!********************************************!*\
  !*** ./src/client/actions/InviteDelete.js ***!
  \********************************************/

/*!********************************************!*\
  !*** ./src/managers/GuildMemberManager.js ***!
  \********************************************/

/*!********************************************!*\
  !*** ./src/structures/MessageCollector.js ***!
  \********************************************/

/*!********************************************!*\
  !*** ./voice/ClientVoiceManager (ignored) ***!
  \********************************************/

/*!*********************************************!*\
  !*** ../sharding/ShardClientUtil (ignored) ***!
  \*********************************************/

/*!*********************************************!*\
  !*** ./src/client/actions/ChannelCreate.js ***!
  \*********************************************/

/*!*********************************************!*\
  !*** ./src/client/actions/ChannelDelete.js ***!
  \*********************************************/

/*!*********************************************!*\
  !*** ./src/client/actions/ChannelUpdate.js ***!
  \*********************************************/

/*!*********************************************!*\
  !*** ./src/client/actions/MessageCreate.js ***!
  \*********************************************/

/*!*********************************************!*\
  !*** ./src/client/actions/MessageDelete.js ***!
  \*********************************************/

/*!*********************************************!*\
  !*** ./src/client/actions/MessageUpdate.js ***!
  \*********************************************/

/*!*********************************************!*\
  !*** ./src/managers/GuildChannelManager.js ***!
  \*********************************************/

/*!*********************************************!*\
  !*** ./src/managers/ReactionUserManager.js ***!
  \*********************************************/

/*!*********************************************!*\
  !*** ./src/structures/ClientApplication.js ***!
  \*********************************************/

/*!*********************************************!*\
  !*** ./src/structures/GuildPreviewEmoji.js ***!
  \*********************************************/

/*!*********************************************!*\
  !*** ./src/structures/MessageAttachment.js ***!
  \*********************************************/

/*!*********************************************!*\
  !*** ./src/structures/ReactionCollector.js ***!
  \*********************************************/

/*!**********************************************!*\
  !*** ./src/client/actions/ActionsManager.js ***!
  \**********************************************/

/*!**********************************************!*\
  !*** ./src/client/actions/GuildBanRemove.js ***!
  \**********************************************/

/*!**********************************************!*\
  !*** ./src/client/actions/PresenceUpdate.js ***!
  \**********************************************/

/*!**********************************************!*\
  !*** ./src/client/actions/WebhooksUpdate.js ***!
  \**********************************************/

/*!***********************************************!*\
  !*** ./node_modules/core-util-is/lib/util.js ***!
  \***********************************************/

/*!***********************************************!*\
  !*** ./src/client/actions/GuildRoleCreate.js ***!
  \***********************************************/

/*!***********************************************!*\
  !*** ./src/client/actions/GuildRoleDelete.js ***!
  \***********************************************/

/*!***********************************************!*\
  !*** ./src/client/actions/GuildRoleUpdate.js ***!
  \***********************************************/

/*!***********************************************!*\
  !*** ./src/managers/GuildEmojiRoleManager.js ***!
  \***********************************************/

/*!************************************************!*\
  !*** ./node_modules/timers-browserify/main.js ***!
  \************************************************/

/*!************************************************!*\
  !*** ./node_modules/util-deprecate/browser.js ***!
  \************************************************/

/*!************************************************!*\
  !*** ./src/client/actions/GuildEmojiCreate.js ***!
  \************************************************/

/*!************************************************!*\
  !*** ./src/client/actions/GuildEmojiDelete.js ***!
  \************************************************/

/*!************************************************!*\
  !*** ./src/client/actions/GuildEmojiUpdate.js ***!
  \************************************************/

/*!************************************************!*\
  !*** ./src/client/actions/VoiceStateUpdate.js ***!
  \************************************************/

/*!************************************************!*\
  !*** ./src/client/websocket/WebSocketShard.js ***!
  \************************************************/

/*!************************************************!*\
  !*** ./src/client/websocket/handlers/READY.js ***!
  \************************************************/

/*!************************************************!*\
  !*** ./src/client/websocket/handlers/index.js ***!
  \************************************************/

/*!************************************************!*\
  !*** ./src/managers/GuildMemberRoleManager.js ***!
  \************************************************/

/*!************************************************!*\
  !*** ./src/structures/PermissionOverwrites.js ***!
  \************************************************/

/*!************************************************!*\
  !*** ./src/structures/interfaces/Collector.js ***!
  \************************************************/

/*!*************************************************!*\
  !*** ./node_modules/stream-browserify/index.js ***!
  \*************************************************/

/*!*************************************************!*\
  !*** ./src/client/actions/GuildEmojisUpdate.js ***!
  \*************************************************/

/*!*************************************************!*\
  !*** ./src/client/actions/GuildMemberRemove.js ***!
  \*************************************************/

/*!*************************************************!*\
  !*** ./src/client/actions/GuildMemberUpdate.js ***!
  \*************************************************/

/*!*************************************************!*\
  !*** ./src/client/actions/MessageDeleteBulk.js ***!
  \*************************************************/

/*!*************************************************!*\
  !*** ./src/structures/PartialGroupDMChannel.js ***!
  \*************************************************/

/*!**************************************************!*\
  !*** ./node_modules/abort-controller/browser.js ***!
  \**************************************************/

/*!**************************************************!*\
  !*** ./src/client/actions/MessageReactionAdd.js ***!
  \**************************************************/

/*!**************************************************!*\
  !*** ./src/client/websocket/WebSocketManager.js ***!
  \**************************************************/

/*!**************************************************!*\
  !*** ./src/client/websocket/handlers/RESUMED.js ***!
  \**************************************************/

/*!**************************************************!*\
  !*** ./src/structures/IntegrationApplication.js ***!
  \**************************************************/

/*!**************************************************!*\
  !*** ./src/structures/interfaces/Application.js ***!
  \**************************************************/

/*!***************************************************!*\
  !*** ./node_modules/inherits/inherits_browser.js ***!
  \***************************************************/

/*!***************************************************!*\
  !*** ./node_modules/readable-stream/transform.js ***!
  \***************************************************/

/*!***************************************************!*\
  !*** ./node_modules/setimmediate/setImmediate.js ***!
  \***************************************************/

/*!****************************************************!*\
  !*** ./node_modules/process-nextick-args/index.js ***!
  \****************************************************/

/*!*****************************************************!*\
  !*** ./node_modules/readable-stream/passthrough.js ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./src/client/actions/MessageReactionRemove.js ***!
  \*****************************************************/

/*!******************************************************!*\
  !*** ./node_modules/node-libs-browser/mock/empty.js ***!
  \******************************************************/

/*!******************************************************!*\
  !*** ./node_modules/util/support/isBufferBrowser.js ***!
  \******************************************************/

/*!******************************************************!*\
  !*** ./src/client/websocket/handlers/USER_UPDATE.js ***!
  \******************************************************/

/*!*******************************************************!*\
  !*** ./src/client/actions/GuildIntegrationsUpdate.js ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./src/client/websocket/handlers/GUILD_CREATE.js ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./src/client/websocket/handlers/GUILD_DELETE.js ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./src/client/websocket/handlers/GUILD_UPDATE.js ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./src/client/websocket/handlers/TYPING_START.js ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./src/structures/interfaces/TextBasedChannel.js ***!
  \*******************************************************/

/*!********************************************************!*\
  !*** ./node_modules/readable-stream/duplex-browser.js ***!
  \********************************************************/

/*!********************************************************!*\
  !*** ./src/client/actions/GuildRolesPositionUpdate.js ***!
  \********************************************************/

/*!********************************************************!*\
  !*** ./src/client/actions/MessageReactionRemoveAll.js ***!
  \********************************************************/

/*!********************************************************!*\
  !*** ./src/client/websocket/handlers/GUILD_BAN_ADD.js ***!
  \********************************************************/

/*!********************************************************!*\
  !*** ./src/client/websocket/handlers/INVITE_CREATE.js ***!
  \********************************************************/

/*!********************************************************!*\
  !*** ./src/client/websocket/handlers/INVITE_DELETE.js ***!
  \********************************************************/

/*!*********************************************************!*\
  !*** ./src/client/websocket/handlers sync ^\.\/.*\.js$ ***!
  \*********************************************************/

/*!*********************************************************!*\
  !*** ./src/client/websocket/handlers/CHANNEL_CREATE.js ***!
  \*********************************************************/

/*!*********************************************************!*\
  !*** ./src/client/websocket/handlers/CHANNEL_DELETE.js ***!
  \*********************************************************/

/*!*********************************************************!*\
  !*** ./src/client/websocket/handlers/CHANNEL_UPDATE.js ***!
  \*********************************************************/

/*!*********************************************************!*\
  !*** ./src/client/websocket/handlers/MESSAGE_CREATE.js ***!
  \*********************************************************/

/*!*********************************************************!*\
  !*** ./src/client/websocket/handlers/MESSAGE_DELETE.js ***!
  \*********************************************************/

/*!*********************************************************!*\
  !*** ./src/client/websocket/handlers/MESSAGE_UPDATE.js ***!
  \*********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/@discordjs/collection/dist/index.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/@discordjs/form-data/lib/browser.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/readable-stream/readable-browser.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/readable-stream/writable-browser.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./src/client/actions/MessageReactionRemoveEmoji.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./src/client/websocket/handlers/PRESENCE_UPDATE.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./src/client/websocket/handlers/WEBHOOKS_UPDATE.js ***!
  \**********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/string_decoder/lib/string_decoder.js ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./src/client/actions/GuildChannelsPositionUpdate.js ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./src/client/websocket/handlers/GUILD_BAN_REMOVE.js ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./src/client/websocket/handlers/GUILD_MEMBER_ADD.js ***!
  \***********************************************************/

/*!************************************************************!*\
  !*** ./node_modules/readable-stream/lib/_stream_duplex.js ***!
  \************************************************************/

/*!************************************************************!*\
  !*** ./src/client/websocket/handlers/GUILD_ROLE_CREATE.js ***!
  \************************************************************/

/*!************************************************************!*\
  !*** ./src/client/websocket/handlers/GUILD_ROLE_DELETE.js ***!
  \************************************************************/

/*!************************************************************!*\
  !*** ./src/client/websocket/handlers/GUILD_ROLE_UPDATE.js ***!
  \************************************************************/

/*!*************************************************************!*\
  !*** ./src/client/websocket/handlers/VOICE_STATE_UPDATE.js ***!
  \*************************************************************/

/*!**************************************************************!*\
  !*** ./node_modules/readable-stream/lib/_stream_readable.js ***!
  \**************************************************************/

/*!**************************************************************!*\
  !*** ./node_modules/readable-stream/lib/_stream_writable.js ***!
  \**************************************************************/

/*!**************************************************************!*\
  !*** ./src/client/websocket/handlers/CHANNEL_PINS_UPDATE.js ***!
  \**************************************************************/

/*!**************************************************************!*\
  !*** ./src/client/websocket/handlers/GUILD_EMOJIS_UPDATE.js ***!
  \**************************************************************/

/*!**************************************************************!*\
  !*** ./src/client/websocket/handlers/GUILD_MEMBERS_CHUNK.js ***!
  \**************************************************************/

/*!**************************************************************!*\
  !*** ./src/client/websocket/handlers/GUILD_MEMBER_REMOVE.js ***!
  \**************************************************************/

/*!**************************************************************!*\
  !*** ./src/client/websocket/handlers/GUILD_MEMBER_UPDATE.js ***!
  \**************************************************************/

/*!**************************************************************!*\
  !*** ./src/client/websocket/handlers/MESSAGE_DELETE_BULK.js ***!
  \**************************************************************/

/*!**************************************************************!*\
  !*** ./src/client/websocket/handlers/VOICE_SERVER_UPDATE.js ***!
  \**************************************************************/

/*!***************************************************************!*\
  !*** ./node_modules/readable-stream/lib/_stream_transform.js ***!
  \***************************************************************/

/*!***************************************************************!*\
  !*** ./src/client/websocket/handlers/MESSAGE_REACTION_ADD.js ***!
  \***************************************************************/

/*!*****************************************************************!*\
  !*** ./node_modules/readable-stream/lib/_stream_passthrough.js ***!
  \*****************************************************************/

/*!******************************************************************!*\
  !*** ./src/client/websocket/handlers/MESSAGE_REACTION_REMOVE.js ***!
  \******************************************************************/

/*!********************************************************************!*\
  !*** ./src/client/websocket/handlers/GUILD_INTEGRATIONS_UPDATE.js ***!
  \********************************************************************/

/*!*********************************************************************!*\
  !*** ./node_modules/util/node_modules/inherits/inherits_browser.js ***!
  \*********************************************************************/

/*!**********************************************************************!*\
  !*** ./node_modules/readable-stream/lib/internal/streams/destroy.js ***!
  \**********************************************************************/

/*!**********************************************************************!*\
  !*** ./src/client/websocket/handlers/MESSAGE_REACTION_REMOVE_ALL.js ***!
  \**********************************************************************/

/*!************************************************************************!*\
  !*** ./src/client/websocket/handlers/MESSAGE_REACTION_REMOVE_EMOJI.js ***!
  \************************************************************************/

/*!*************************************************************************!*\
  !*** ./node_modules/readable-stream/lib/internal/streams/BufferList.js ***!
  \*************************************************************************/

/*!*****************************************************************************!*\
  !*** ./node_modules/readable-stream/lib/internal/streams/stream-browser.js ***!
  \*****************************************************************************/
