exports.permission = {
    channelid  : ["1372175124052181114"],
    roleid : ["1127380275865845940", "1326971562779218001", "1329313541177479189"]
}

exports.help = "[ID] [Tên Job2] [C<PERSON><PERSON> bậc]"

exports.run = async (message, args) => {
    try {
        let playerId = args[1]
        if (!playerId) return message.reply("`Vui lòng nhập ID`")     

        let job2 = args[2]
        if (!job2) return message.reply("`Vui lòng nhập tên Job2`")     

        let grade = args[3]
        if (!grade) return message.reply("`Vui lòng nhập Cập bậc`")     

      

        let data = await fetch(`${host}/setjob2`, {
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                data : {
                    'id' :playerId,
                    'job2' : job2,
                    'grade' : grade,
                }
            }),
            method : "POST",
        }).then(data => data.json())
        if (data.error) {
            vralitylog.error("Respone " + path.basename(__filename), data.error)
            message.reply("`<PERSON>ó lỗi x<PERSON>y ra`")
            message.reply('```fix\n' + data.error + "```")
            return
        }
        message.reply(data.success + func.getTimeString())
    }
    catch (err) { 
        vralitylog.error(path.basename(__filename), err)
        connected = false;
    }
}