exports.permission = {
    channelid  : ["1372175124052181114"],
    roleid : ["1127380275865845940", "1326971562779218001", "1329313541177479189"]
}

exports.help = "[ID]"


exports.run = async (message, args) => {
    try {
        let playerId = args[1]
        if (!playerId) return message.reply("`Vui lòng nhập ID`")     

        let data = await fetch(`${host}/revive`, {
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                data : {
                    id :playerId,
                }
            }),
            method : "POST",
        }).then(data => data.json())
        if (data.error) {
            vralitylog.error("Respone " + path.basename(__filename), data.error)
            message.reply("`Có lỗi xảy ra`")
            message.reply('```fix\n' + data.error + "```")
            return
        }
        message.reply(data.success + func.getTimeString())
    }
    catch (err) { 
        vralitylog.error(path.basename(__filename), err)
        connected = false;
    }
}

    
    