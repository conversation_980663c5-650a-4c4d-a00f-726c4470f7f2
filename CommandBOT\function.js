/////////////export/////////////////////
module.exports = 
{
  GetRandomString,
  delay,
  cach,
  checkPermission,
  getTimeString,
  SendHelp
}
////////////////////////////////////////



async function GetRandomString(LENGTHTEXT)
{
  let codereturn = ''
  let textcache   = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  for (let i = 1; i<= LENGTHTEXT ; i++)
  codereturn  += textcache.charAt(Math.floor(Math.random() *textcache.length))
  return codereturn;
}

function cach(maxcach , length)
{
  let khoangcach ="                                                                                                                                                                                   "
  return khoangcach.substring(0, maxcach - length)
}

function delay(delayInms) {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve(2);
    }, delayInms);
  });
}


function getTimeString()
{
  let d         = new Date();
  let minutenow = d.getMinutes();
  let hournow   = d.getHours();
  let datenow   = d.getDate();
  let monthnow  = d.getMonth()+1;
  let yearnow   = d.getFullYear();
  return ( ' `' + hournow + ':' + minutenow + ' Ngày ' + datenow + '/' + monthnow + '/' + yearnow  + ' `');
}

function checkPermission(data , message) {

  checkchannel = true
  checkrole    = true
  if (data.channelid && data.channelid.length !=0 ) {
    checkchannel = false;
    for (let i = 0; i < data.channelid.length; i++)  
      if (data.channelid[i] == message.channel.id) {
        checkchannel = true;
        break
      }
  }

  if (data.roleid && data.roleid.length != 0 && !message.member.hasPermission("support fish"))  { 
    checkrole = false;
    for (let i = 0; i < data.roleid.length; i++)  
    if (message.guild.member(message.author)._roles.includes(`${data.roleid[i]}`)) {
      checkrole = true;
      break
    }
  }

  return checkchannel&&checkrole;
}


async function SendHelp(message) {

  let cmds = await fs.readdirSync('./cmds/')

  content = "**Danh sách các lệnh Admin**```yaml"

  for (let i = 0 ; i < cmds.length ; i++)
  {
    let cmd = cmds[i].split(".")[0]
    content += `\n${config.prefix}${cmd} ${new require(`./cmds/${cmds[i]}`).help}`
    clearRequire(`./cmds/${cmds[i]}`)
  }

  content +="```"

  message.reply(content)
}
  
