const { Client, GatewayIntentBits } = require('discord.js');
const fetch = require('node-fetch');

const client = new Client({ intents: [GatewayIntentBits.Guilds, GatewayIntentBits.GuildMessages, GatewayIntentBits.MessageContent] });

const TOKEN = 'YOUR_BOT_TOKEN'; // Thay bằng token bot của bạn
const API_URL = 'http://localhost:18952/nvboutique_addcoin'; // Thay đúng endpoint nếu khác

client.on('messageCreate', async (message) => {
    if (!message.content.startsWith('!addcoin')) return;

    const args = message.content.trim().split(/ +/g);
    const playerId = args[1];
    const coinAmount = args[2];

    if (!playerId || !coinAmount) {
        return message.reply('Cú pháp: !addcoin <ID> <Số lượng>');
    }

    try {
        const res = await fetch(API_URL, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                data: {
                    id: playerId,
                    amount: parseInt(coinAmount)
                }
            })
        });
        const data = await res.json();
        if (data.error) {
            return message.reply('Có lỗi xảy ra: ' + data.error);
        }
        message.reply('Đã cộng ' + coinAmount + ' coin cho ID ' + playerId);
    } catch (err) {
        message.reply('Lỗi kết nối API!');
    }
});

client.login(TOKEN); 