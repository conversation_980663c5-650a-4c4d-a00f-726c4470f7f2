const express = require('express');
const bodyParser = require('body-parser');
const path = require('path');   
const fetch = require('node-fetch');
const PORT = 24004
const app = express();
 let ESX = null;


main()

giveCarResponse = {}

async function main() {

    await LoadESX();

    onNet('DisordBOT:giveCarResponse', (giveId , plate) => {
        console.log(giveCarResponse[giveId])
        if (giveCarResponse[giveId]) {

            if (plate)
               message = `Đã chuyển phương tiện **${giveCarResponse[giveId].carName}** ( Biển số: **${plate}**, model: **${giveCarResponse[giveId].model}**) cho ${giveCarResponse[giveId].infoPlayer}` + getTimeString()
            else
                message =  `\`Phương tiện ${giveCarResponse[giveId].carName} không tồn tại hoặc biển số đã có người sỡ hữu\``
        
            SendDiscordMessage(message,giveCarResponse[giveId].channelId)
            giveCarResponse[giveId]= null
        }
   

    })

app
.get("/testconnection", async (req, res) => {
    res.json({ "status": "OK"})
})
.get("*", async (req, res) => {
    res.send("")
})
.use(bodyParser.json())
.use(bodyParser.urlencoded({
  extended: true
}))
.post("/revive" , async (req, res) =>{  
    try
    {
        if (!ESX) {
            LoadESX()
            res.json({"error" : "Đang Load ESX vui lòng thử lại sau" })
        }


        let data = req.body.data
        // let playerId = parseInt(data.id)
        // let playerName = GetPlayerName(playerId)
        let xPlayer = ESX.GetPlayerFromId(data.id)
        
        if (!xPlayer.name) return res.json({"error" : "ID người chơi không Online" })
        emitNet('esx_ambulancejob:revive',xPlayer.source)
        console.log(`DiscordBOT: hồi sinh ${xPlayer.name}`)
        res.json({"success" : `Đã hồi sinh người chơi **${xPlayer.name}**`})
    }
    catch (err)
    {
        console.log(err)
        res.json({"error" : err })
    }
})

.post("/tp" , async (req, res) =>{
    try
    {
        if (!ESX) {
            LoadESX()
            res.json({"error" : "Đang Load ESX vui lòng thử lại sau" })
        }

        let data = req.body.data
        let xPlayer = ESX.GetPlayerFromId(data.id)
        if (!xPlayer.name) return res.json({"error" : "ID người chơi không Online" })

        // Sử dụng tọa độ từ data nếu có, nếu không thì dùng tọa độ mặc định (bệnh viện)
        let coords = {
            x: data.x || 213.69,
            y: data.y || -808.49,
            z: data.z || 31.01
        }

        emitNet('bringAmbulanceCl', xPlayer.source, coords)
        console.log(`DiscordBOT: Teleport người chơi ${xPlayer.name} đến tọa độ: ${coords.x}, ${coords.y}, ${coords.z}`)
        res.json({"success" : `Đã teleport người chơi **${xPlayer.name}** đến tọa độ (${coords.x}, ${coords.y}, ${coords.z})`})
    }
    catch (err)
    {
        console.log(err)
        res.json({"error" : err })
    }
})
.post("/name" , async (req, res) =>{  
    try
    {
        if (!ESX ) {
            LoadESX()
            res.json({"error" : "Đang Load ESX vui lòng thử lại sau" })
        }

        let data = req.body.data
        // let playerId = parseInt(data.id)
        // let playerName = GetPlayerName(playerId)
        let xPlayer = ESX.GetPlayerFromId(data.id)
        if (!xPlayer.name) return res.json({"error" : "ID người chơi không Online" })
        res.json({"success" : `Thông tin: **${xPlayer.name} - ${GetIdentifiers(xPlayer.source)}**.`})
    }
    catch (err)
    {
        console.log(err)
        res.json({"error" : err })
    }
})
.post("/kick" , async (req, res) =>{  
    try
    {
        if (!ESX ) {
            LoadESX()
            res.json({"error" : "Đang Load ESX vui lòng thử lại sau" })
        }

        let data = req.body.data
        // let playerId = parseInt(data.id)
        // let playerName = GetPlayerName(playerId)
        let xPlayer = ESX.GetPlayerFromId(data.id)

        if (!xPlayer.name) return res.json({"error" : "ID người chơi không Online" })
        emitNet('chat:addMessage', -1, {
            args : ["^1DISCORD",`^2${xPlayer.name}^0 bị kick bởi ^2${data.name}^0. Lý Do: ${data.reason}`]
        })
        xPlayer.kick(`Bạn đã bị kick bởi ${data.name}. Lý Do: ${data.reason}`)

        res.json({"success" : `Đã kick người chơi **${xPlayer.name} - ${xPlayer.getIdentifier()}**.`})
    }
    catch (err)
    {
        console.log(err)
        res.json({"error" : err })
    }
})

// .post("/givetien" , async (req, res) =>{  
//     try
//     {
//         if (!ESX) {
//             LoadESX()
//             res.json({"error" : "Đang Load ESX vui lòng thử lại sau" })
//         }

//         let data = req.body.data
//         let amount = parseInt(data.amount || 0)
//         let xPlayer = ESX.GetPlayerFromId(data.id)
//         if (!xPlayer.name) return res.json({"error" : "ID người chơi không Online" })
        
//         xPlayer.addAccountMoney("money" , amount)
//         xPlayer.showNotification(`Bạn đã nhận được $${amount} từ Admin`)
//         res.json({"success" : `Đã chuyển ${amount}$ cho **${xPlayer.name} - ${xPlayer.getIdentifier()}**`})
//     }
//     catch (err)
//     {
//         console.log(err)
//         res.json({"error" : err })
//     }
// })
// .post("/givecoin" , async (req, res) =>{  
//     try
//     {
//         if (!ESX) {
//             LoadESX()
//             res.json({"error" : "Đang Load ESX vui lòng thử lại sau" })
//         }

//         let data = req.body.data
//         let amount = parseInt(data.amount || 0)
//         let xPlayer = ESX.GetPlayerFromId(data.id)
//         if (!xPlayer.name) return res.json({"error" : "ID người chơi không Online" })
        
//         xPlayer.addAccountMoney("coin" , amount)
//         xPlayer.showNotification(`Bạn đã nhận được ~g~*${amount}~s~ từ ~b~Admin~s~`)
//         res.json({"success" : `Đã chuyển coin ${amount}$ vào cho **${xPlayer.name} - ${xPlayer.getIdentifier()}**`})
//     }
//     catch (err)
//     {
//         console.log(err)
//         res.json({"error" : err })
//     }
// })
.post("/giveitem" , async (req, res) =>{  
    try
    {
        if (!ESX || !ESX.Items || ESX.Items.length == 0) {
            LoadESX()
            res.json({"error" : "Đang Load ESX vui lòng thử lại sau" })
        }

        let data = req.body.data
        let itemAmount = parseInt(data.amount || 0)
        let itemName = data.name
        let xPlayer = ESX.GetPlayerFromId(data.id)
        if (!xPlayer.name) return res.json({"error" : "ID người chơi không Online" })
        if (!ESX.GetItemLabel(itemName)) return res.json({"error" : "Item không tồn tại" })
        
        // exports.ox_inventory.AddItem(data.id, itemName, itemAmount)
        TriggerEvent('discord:giveitem',data.id, itemName, itemAmount)
        xPlayer.showNotification(`Bạn đã nhận được ${itemAmount}x ${itemName} từ Admin`)

        res.json({"success" : `Đã chuyển ${itemAmount}x ${itemName} cho **${xPlayer.name} - ${xPlayer.getIdentifier()}**`})
    }
    catch (err)
    {
        console.log(err)
        res.json({"error" : err })
    }
})
// .post("/ban" , async (req, res) =>{  
//     try
//     {
//         if (!ESX) {
//             LoadESX()
//             res.json({"error" : "Đang Load ESX vui lòng thử lại sau" })
//         }

//         let data = req.body.data
//         let xPlayer = ESX.GetPlayerFromId(data.id)
//         if (!xPlayer.name) return res.json({"error" : "ID người chơi không Online" })
//         TriggerEvent('banDis', data.id, data.duree, data.reason)
//         res.json({"success" : `Đã ban nguoi choi **${xPlayer.name} - ${xPlayer.getIdentifier()}**`})
//     }
//     catch (err)
//     {
//         console.log(err)
//         res.json({"error" : err })
//     }
// })
.post("/giveall" , async (req, res) =>{  
    try
    {
        if (!ESX) {
            LoadESX()
            res.json({"error" : "Đang Load ESX vui lòng thử lại sau" })
        }

        let data = req.body.data
        let item = data.tenitem
        let soluong = parseInt(data.soluong)
        if (!ESX.GetItemLabel(item)) return res.json({"error" : "Item không tồn tại" })
        TriggerEvent("esx:giveallitem", source, item,soluong)
        res.json({"success" : `Đã chuyển item ***${item}*** cho tất cả mọi người`})
    }
    catch (err)
    {
        console.log(err)
        res.json({"error" : err })
    }
})
// .post("/startchest" , async (req, res) =>{  
//     try
//     {
//         if (!ESX) {
//             LoadESX()
//             res.json({"error" : "Đang Load ESX vui lòng thử lại sau" })
//         }
//         let data = req.body.data
//         let soluong = parseInt(data.soluong)
//         TriggerEvent("lr_chest:server:chay", source, soluong)
//         res.json({"success" : `Đã chạy đảo kho báu với số lượng hòm : ***${soluong}***`})
//     }
//     catch (err)
//     {
//         console.log(err)
//         res.json({"error" : err })
//     }
// })
// .post("/guitinnhan" , async (req, res) =>{  
//     try
//     {
//         if (!ESX) {
//             LoadESX()
//             res.json({"error" : "Đang Load ESX vui lòng thử lại sau" })
//         }

//         let data = req.body.data
//         let author = data.name
//         let message = data.content

//         emitNet('chat:addMessage', -1,	{
//             template : '<div style="padding: 8px; margin: 8px; background-color: rgba(11, 65, 43, 0.6); border-radius: 25px;">[{0}]: {1}</font></i></b></div>',
//             args : [author, message]
//         })

//         res.json({"success" : `**Gửi tin nhắn thành công**`})
//     }
//     catch (err)
//     {
//         console.log(err)
//         res.json({"error" : err })
//     }
// })
// .post("/setgroup" , async (req, res) =>{  
//     try
//     {
//         if (!ESX || !ESX.Items || ESX.Items.length == 0) {
//             LoadESX()
//             res.json({"error" : "Đang Load ESX vui lòng thử lại sau" })
//         }

//         let data = req.body.data
//         let playerId = parseInt(data.id)
//         let groupName = data.group 
//         let xPlayer = ESX.GetPlayerFromId(data.id)
//         if (!xPlayer.name) return res.json({"error" : "ID người chơi không Online" })
//         xPlayer.setGroup(groupName)
//         res.json({"success" : `Đã set Group Thành Công cho **${xPlayer.name} - ${xPlayer.getIdentifier()}**`})
//     }
//     catch (err)
//     {
//         console.log(err)
//         res.json({"error" : err })
//     }
// })
.post("/setjob" , async (req, res) =>{  
    try
    {
        if (!ESX || !ESX.Items || ESX.Items.length == 0) {
            LoadESX()
            res.json({"error" : "Đang Load ESX vui lòng thử lại sau" })
        }

        let data = req.body.data
        let jobName = data.job
        let jobGrade = data.grade 
        let xPlayer = ESX.GetPlayerFromId(data.id)
        if (!xPlayer.name) return res.json({"error" : "ID người chơi không Online" })

        if (!ESX.DoesJobExist(jobName,jobGrade) )
            return res.json({"error" : `Tên hoặc Cấp Bậc Job không tồn tại`})
           
        xPlayer.setJob(jobName,jobGrade)
    
        res.json({"success" : `Đã Set Job thành công cho **${xPlayer.name} - ${xPlayer.getIdentifier()}**`})
    }
    catch (err)
    {
        console.log(err)
        res.json({"error" : err })
    }
})

.post("/setjob2" , async (req, res) =>{  
    try
    {
        if (!ESX || !ESX.Items || ESX.Items.length == 0) {
            LoadESX()
            res.json({"error" : "Đang Load ESX vui lòng thử lại sau" })
        }

        let data = req.body.data
        let jobName = data.job2
        let jobGrade = data.grade 
        let xPlayer = ESX.GetPlayerFromId(data.id)
        if (!xPlayer.name) return res.json({"error" : "ID người chơi không Online" })

        if (!ESX.DoesJobExist(jobName,jobGrade) )
            return res.json({"error" : `Tên hoặc Cấp Bậc Job không tồn tại`})
           
        xPlayer.setJob2(jobName,jobGrade)
    
        res.json({"success" : `Đã Set Job thành công cho **${xPlayer.name} - ${xPlayer.getIdentifier()}**`})
    }
    catch (err)
    {
        console.log(err)
        res.json({"error" : err })
    }
})
// .post("/xoafullxe" , async (req, res) =>{  
//     try
//     {
//         if (!ESX || !ESX.Items || ESX.Items.length == 0) {
//             LoadESX()
//             res.json({"error" : "Đang Load ESX vui lòng thử lại sau" })
//         }

//         emitNet("wld:delallveh", -1)
    
//         res.json({"success" : `**Đã Xóa All Xe**`})
//     }
//     catch (err)
//     {
//         console.log(err)
//         res.json({"error" : err })
//     }
// })
.post("/givecar" , async (req, res) =>{  
    try
    {
        if (!ESX) {
            LoadESX()
            res.json({"error" : "Đang Load ESX vui lòng thử lại sau" })
        }

        let data = req.body.data
        let carName = data.model
        let carPlate = data.plate || ""
        let carModel = GetHashKey(carName)
  
 
        let xPlayer = ESX.GetPlayerFromId(data.id)
        let ten = xPlayer.name
        if (!xPlayer.name) return res.json({"error" : "ID người chơi không Online" })

        let giveId = await GetRandomString(5)

        while (giveCarResponse[giveId]) 
            giveId = await GetRandomString(5)
        
        
        res.json({"success" : `**Đang khởi tạo phương tiện**`})

        giveCarResponse[giveId]= {
            'infoPlayer' : `**${xPlayer.name} - ${xPlayer.getIdentifier()}**`,
            'carName' : carName,
            "model" : carModel,
            "channelId": data.channelid
        }

        emitNet("DiscordBOT:givecar", xPlayer.source , carName, carPlate , giveId,ten)

    }
    catch (err)
    {
        console.log(err)
        res.json({"error" : err })
    }
})

.post("/nvboutique_addcoin", async (req, res) => {
    try {
        let data = req.body.data;
        let id = parseInt(data.id);
        let amount = parseInt(data.amount);
        // Gọi sự kiện cộng coin trong scripts NVBoutique (FiveM fxserver)
        if (typeof global.TriggerEvent === 'function') {
            global.TriggerEvent('Boutique:DiscordAddCoin', id, amount);
            res.json({ success: `Đã cộng ${amount} coin cho ID ${id}` });
        } else {
            res.json({ error: 'Không thể gọi TriggerEvent từ môi trường Node.js này. Hãy chạy DiscordBOT như một resource của FiveM.' });
        }
    } catch (err) {
        res.json({ error: err.message });
    }
})

.listen(PORT, () =>console.log(`Listening on ${ PORT }`))

}

function wait(delayInms) {
    return new Promise(resolve => {
      setTimeout(() => {
        resolve(2);
      }, delayInms);
    });
}

function GetIdentifiers(playerId) {
    playerId = parseInt(playerId)
    let numIds = GetNumPlayerIdentifiers(playerId);

    for (let i = 0; i < numIds; i++) 
        if(GetPlayerIdentifier(playerId, i).indexOf("steam") != -1 ) return GetPlayerIdentifier(playerId, i);

    return "";
}


async function GetRandomString(LENGTHTEXT)
{
  let codereturn = ''
  let textcache   = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  for (let i = 1; i<= LENGTHTEXT ; i++)
  codereturn  += textcache.charAt(Math.floor(Math.random() *textcache.length))
  return codereturn;
}

function getTimeString()
{
  let d         = new Date();
  let minutenow = d.getMinutes();
  let hournow   = d.getHours();
  let datenow   = d.getDate();
  let monthnow  = d.getMonth()+1;
  let yearnow   = d.getFullYear();
  return ( ' `' + hournow + ':' + minutenow + ' Ngày ' + datenow + '/' + monthnow + '/' + yearnow  + ' `');
}


function LoadESX() {
   ESX = exports.es_extended.getSharedObject()
       
    //  emit("esx:getSharedObject", (obj) => ESX = obj);
}

function SendDiscordMessage(content, channel) {
    fetch(`https://discord.com/api/channels/${channel}/messages`, {
        headers: {
            'Content-Type': 'application/json',
            Authorization: "Bot MTM4ODA2MDg4MjMzMTc2Mjc3MA.GE4woJ.BKAmXPN6PX2iPnkIIImxcEHwjVKjyhLuohiols",
        },
        body: JSON.stringify({
          "content": content
        }),
        method : "POST"
    })
}