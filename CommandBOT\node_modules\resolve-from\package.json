{"name": "resolve-from", "version": "2.0.0", "description": "Resolve the path of a module like require.resolve() but from a given path", "license": "MIT", "repository": "sindresorhus/resolve-from", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["require", "resolve", "path", "module", "from", "like", "path"], "devDependencies": {"ava": "*", "xo": "*"}}