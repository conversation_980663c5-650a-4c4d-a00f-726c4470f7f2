Discord = require("discord.js");
fetch = require("node-fetch");
converter = require('hex2dec');
clearRequire = require('clear-require');
fs = require('fs');
path = require('path');

vralitylog    = require("./log.js");
func = require('./function.js');
config        = require("./config.json");

host = "http://**************:24004"


client = new Discord.Client();


//////////////CACHE BOT////////////	
connected = false
///////////////////////////////////
  

async function main()
{



  client.on("ready", async () => 
  {
    try {
      vralitylog.log([
        {
          textColor : "black",
          bgColor : "cyan",
          message: client.user.tag,
        },
        {
          textColor : 'green',
          message: ` ONLINE!`
        }
      ]);  

    }
    catch (err) {
      vralitylog.error( "Send Confirm Message Started" , err)
    }

    // Load Resources

    setInterval( async () => { 

      try {
        if (connected) return client.user.setActivity('IRON');
        client.user.setActivity('BOT disconnected');

        let data = await fetch(`${host}/testconnection`).then(data => data.json())
        if (data.status && data.status == "OK") connected = true;
      } 
      catch (err) {
        vralitylog.error("Test Conection" , err)
      }


    }, 3*1000);


  })








  


  client.on("message", async (message) => {
    try
    {
      ////////Hàm reaction + gửi chat log /////////////////
      async function seen()
      {
        let d         = new Date();
        let minutenow = d.getMinutes();
        let hournow   = d.getHours();
        let datenow   = d.getDate();
        let monthnow  = d.getMonth()+1;
        let yearnow   = d.getFullYear();
        let timenow   = (hournow + ':' + minutenow + ' Ngày ' + datenow + '/' + monthnow + '/' + yearnow);
        let chatlog   = ( '**' + message.author.username + "** *đã gửi lệnh* : `" + message.content + '` | tới <#' + message.channel.id +  '>' + '|| `' + timenow + '`' ) ;

        // Kiểm tra channel tồn tại trước khi gửi log
        if (config.logid && config.logid.trim() !== "") {
            const logChannel = client.channels.cache.get(config.logid);
            if (logChannel) {
                logChannel.send(chatlog).catch(err => {
                    console.log("Không thể gửi log:", err.message);
                });
            } else {
                console.log("Không tìm thấy log channel với ID:", config.logid);
            }
        }
        reactarr = ['🤔', '👊', '🥰', '😍', '🤭', '💩', '👋', '✌️', '👌', '👀', '👏', '👩‍⚕️', '💪', '🐶', '🚑', '💕', '💓', '💯', '✅', '🆗']
        message.react(reactarr[Math.floor(Math.random() * reactarr.length)])
      }
      //////////////////////////////////////////////////////////////////////    

      ////////////////CHECK ///////////////////
      if (message.author.bot) return;  ///BOT RETURN
      if (message.channel.type == 'dm') return ;  ////INBOX RETURN
      if (message.content.indexOf(config.prefix) !== 0) return; ///NON-PREFIX RETURN
      const args = message.content.slice(config.prefix.length).trim().split(/ +/g); /// TO ARGS
      const command = args[0].toLowerCase();  //// GET COMMAND
      if (command == '') return /// RETURN NULL COMMAND
      ///////////////////////////////////////


      if (command == 'help') return func.SendHelp(message)

      if (!connected) return message.reply("`BOT chưa được kết nối với server`")
      
      if (fs.existsSync(`./cmds/${command}.js`)) {
        try { 
          let commandFile = new require(`./cmds/${command}.js`);

          
          if (!func.checkPermission(commandFile.permission, message)) 
            message.reply("`Channel hoặc quyền hạn không hợp lệ`");
          else {
            seen()
            commandFile.run(message, args);
          }
            

          clearRequire(`./cmds/${command}.js`)
        }
         catch (err) {
          vralitylog.error( "CMDS RUN: " + command , err)
        }
        return;
      }
      else { 
        message.reply("`Lệnh không tồn tại!`")
      }


    }
    catch (err)
    {
      vralitylog.error( "Body BOT", err)
    }
  });

  client.login(config.token);
}

main();

