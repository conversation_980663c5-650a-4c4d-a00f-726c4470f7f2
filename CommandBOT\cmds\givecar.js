exports.permission = {
    channelid  : ["1372175124052181114"],
    roleid : ["1127380275865845940", "1326971562779218001", "1329313541177479189"]
}

exports.help = "[ID] [Model Xe] [<PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON> trống nếu muốn random)]"

exports.run = async (message, args) => {
    try {
        let playerId = args[1]
        if (!playerId) return message.reply("`<PERSON>ui lòng nhập ID`")     

        let model = args[2]
        if (!model) return message.reply("`Vui lòng nhập tên vật phẩm`")     

        let plate = args.filter((v,i) => i >= 3).join(" ") || ""

        let data = await fetch(`${host}/givecar`, {
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                data : {
                    'id' :playerId,
                    'model' : model,
                    'plate' : plate,
                    'channelId' : message.channel.id
                }
            }),
            method : "POST",
        }).then(data => data.json())
        if (data.error) {
            vralitylog.error("Respone " + path.basename(__filename), data.error)
            message.reply("`Có lỗi xảy ra`")
            message.reply('```fix\n' + data.error + "```")
            return
        }
        message.reply(data.success + func.getTimeString())
    }
    catch (err) { 
        vralitylog.error(path.basename(__filename), err)
        connected = false;
    }
}

    
    

    
    
