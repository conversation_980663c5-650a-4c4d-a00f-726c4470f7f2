{"version": 3, "file": "event-target-shim.umd.js", "sources": ["../src/event.mjs", "../src/event-target.mjs"], "sourcesContent": ["/**\n * @typedef {object} PrivateData\n * @property {EventTarget} eventTarget The event target.\n * @property {{type:string}} event The original event object.\n * @property {number} eventPhase The current event phase.\n * @property {EventTarget|null} currentTarget The current event target.\n * @property {boolean} canceled The flag to prevent default.\n * @property {boolean} stopped The flag to stop propagation.\n * @property {boolean} immediateStopped The flag to stop propagation immediately.\n * @property {Function|null} passiveListener The listener if the current listener is passive. Otherwise this is null.\n * @property {number} timeStamp The unix time.\n * @private\n */\n\n/**\n * Private data for event wrappers.\n * @type {WeakMap<Event, PrivateData>}\n * @private\n */\nconst privateData = new WeakMap()\n\n/**\n * Cache for wrapper classes.\n * @type {WeakMap<Object, Function>}\n * @private\n */\nconst wrappers = new WeakMap()\n\n/**\n * Get private data.\n * @param {Event} event The event object to get private data.\n * @returns {PrivateData} The private data of the event.\n * @private\n */\nfunction pd(event) {\n    const retv = privateData.get(event)\n    console.assert(\n        retv != null,\n        \"'this' is expected an Event object, but got\",\n        event\n    )\n    return retv\n}\n\n/**\n * https://dom.spec.whatwg.org/#set-the-canceled-flag\n * @param data {PrivateData} private data.\n */\nfunction setCancelFlag(data) {\n    if (data.passiveListener != null) {\n        if (\n            typeof console !== \"undefined\" &&\n            typeof console.error === \"function\"\n        ) {\n            console.error(\n                \"Unable to preventDefault inside passive event listener invocation.\",\n                data.passiveListener\n            )\n        }\n        return\n    }\n    if (!data.event.cancelable) {\n        return\n    }\n\n    data.canceled = true\n    if (typeof data.event.preventDefault === \"function\") {\n        data.event.preventDefault()\n    }\n}\n\n/**\n * @see https://dom.spec.whatwg.org/#interface-event\n * @private\n */\n/**\n * The event wrapper.\n * @constructor\n * @param {EventTarget} eventTarget The event target of this dispatching.\n * @param {Event|{type:string}} event The original event to wrap.\n */\nfunction Event(eventTarget, event) {\n    privateData.set(this, {\n        eventTarget,\n        event,\n        eventPhase: 2,\n        currentTarget: eventTarget,\n        canceled: false,\n        stopped: false,\n        immediateStopped: false,\n        passiveListener: null,\n        timeStamp: event.timeStamp || Date.now(),\n    })\n\n    // https://heycam.github.io/webidl/#Unforgeable\n    Object.defineProperty(this, \"isTrusted\", { value: false, enumerable: true })\n\n    // Define accessors\n    const keys = Object.keys(event)\n    for (let i = 0; i < keys.length; ++i) {\n        const key = keys[i]\n        if (!(key in this)) {\n            Object.defineProperty(this, key, defineRedirectDescriptor(key))\n        }\n    }\n}\n\n// Should be enumerable, but class methods are not enumerable.\nEvent.prototype = {\n    /**\n     * The type of this event.\n     * @type {string}\n     */\n    get type() {\n        return pd(this).event.type\n    },\n\n    /**\n     * The target of this event.\n     * @type {EventTarget}\n     */\n    get target() {\n        return pd(this).eventTarget\n    },\n\n    /**\n     * The target of this event.\n     * @type {EventTarget}\n     */\n    get currentTarget() {\n        return pd(this).currentTarget\n    },\n\n    /**\n     * @returns {EventTarget[]} The composed path of this event.\n     */\n    composedPath() {\n        const currentTarget = pd(this).currentTarget\n        if (currentTarget == null) {\n            return []\n        }\n        return [currentTarget]\n    },\n\n    /**\n     * Constant of NONE.\n     * @type {number}\n     */\n    get NONE() {\n        return 0\n    },\n\n    /**\n     * Constant of CAPTURING_PHASE.\n     * @type {number}\n     */\n    get CAPTURING_PHASE() {\n        return 1\n    },\n\n    /**\n     * Constant of AT_TARGET.\n     * @type {number}\n     */\n    get AT_TARGET() {\n        return 2\n    },\n\n    /**\n     * Constant of BUBBLING_PHASE.\n     * @type {number}\n     */\n    get BUBBLING_PHASE() {\n        return 3\n    },\n\n    /**\n     * The target of this event.\n     * @type {number}\n     */\n    get eventPhase() {\n        return pd(this).eventPhase\n    },\n\n    /**\n     * Stop event bubbling.\n     * @returns {void}\n     */\n    stopPropagation() {\n        const data = pd(this)\n\n        data.stopped = true\n        if (typeof data.event.stopPropagation === \"function\") {\n            data.event.stopPropagation()\n        }\n    },\n\n    /**\n     * Stop event bubbling.\n     * @returns {void}\n     */\n    stopImmediatePropagation() {\n        const data = pd(this)\n\n        data.stopped = true\n        data.immediateStopped = true\n        if (typeof data.event.stopImmediatePropagation === \"function\") {\n            data.event.stopImmediatePropagation()\n        }\n    },\n\n    /**\n     * The flag to be bubbling.\n     * @type {boolean}\n     */\n    get bubbles() {\n        return Boolean(pd(this).event.bubbles)\n    },\n\n    /**\n     * The flag to be cancelable.\n     * @type {boolean}\n     */\n    get cancelable() {\n        return Boolean(pd(this).event.cancelable)\n    },\n\n    /**\n     * Cancel this event.\n     * @returns {void}\n     */\n    preventDefault() {\n        setCancelFlag(pd(this))\n    },\n\n    /**\n     * The flag to indicate cancellation state.\n     * @type {boolean}\n     */\n    get defaultPrevented() {\n        return pd(this).canceled\n    },\n\n    /**\n     * The flag to be composed.\n     * @type {boolean}\n     */\n    get composed() {\n        return Boolean(pd(this).event.composed)\n    },\n\n    /**\n     * The unix time of this event.\n     * @type {number}\n     */\n    get timeStamp() {\n        return pd(this).timeStamp\n    },\n\n    /**\n     * The target of this event.\n     * @type {EventTarget}\n     * @deprecated\n     */\n    get srcElement() {\n        return pd(this).eventTarget\n    },\n\n    /**\n     * The flag to stop event bubbling.\n     * @type {boolean}\n     * @deprecated\n     */\n    get cancelBubble() {\n        return pd(this).stopped\n    },\n    set cancelBubble(value) {\n        if (!value) {\n            return\n        }\n        const data = pd(this)\n\n        data.stopped = true\n        if (typeof data.event.cancelBubble === \"boolean\") {\n            data.event.cancelBubble = true\n        }\n    },\n\n    /**\n     * The flag to indicate cancellation state.\n     * @type {boolean}\n     * @deprecated\n     */\n    get returnValue() {\n        return !pd(this).canceled\n    },\n    set returnValue(value) {\n        if (!value) {\n            setCancelFlag(pd(this))\n        }\n    },\n\n    /**\n     * Initialize this event object. But do nothing under event dispatching.\n     * @param {string} type The event type.\n     * @param {boolean} [bubbles=false] The flag to be possible to bubble up.\n     * @param {boolean} [cancelable=false] The flag to be possible to cancel.\n     * @deprecated\n     */\n    initEvent() {\n        // Do nothing.\n    },\n}\n\n// `constructor` is not enumerable.\nObject.defineProperty(Event.prototype, \"constructor\", {\n    value: Event,\n    configurable: true,\n    writable: true,\n})\n\n// Ensure `event instanceof window.Event` is `true`.\nif (typeof window !== \"undefined\" && typeof window.Event !== \"undefined\") {\n    Object.setPrototypeOf(Event.prototype, window.Event.prototype)\n\n    // Make association for wrappers.\n    wrappers.set(window.Event.prototype, Event)\n}\n\n/**\n * Get the property descriptor to redirect a given property.\n * @param {string} key Property name to define property descriptor.\n * @returns {PropertyDescriptor} The property descriptor to redirect the property.\n * @private\n */\nfunction defineRedirectDescriptor(key) {\n    return {\n        get() {\n            return pd(this).event[key]\n        },\n        set(value) {\n            pd(this).event[key] = value\n        },\n        configurable: true,\n        enumerable: true,\n    }\n}\n\n/**\n * Get the property descriptor to call a given method property.\n * @param {string} key Property name to define property descriptor.\n * @returns {PropertyDescriptor} The property descriptor to call the method property.\n * @private\n */\nfunction defineCallDescriptor(key) {\n    return {\n        value() {\n            const event = pd(this).event\n            return event[key].apply(event, arguments)\n        },\n        configurable: true,\n        enumerable: true,\n    }\n}\n\n/**\n * Define new wrapper class.\n * @param {Function} BaseEvent The base wrapper class.\n * @param {Object} proto The prototype of the original event.\n * @returns {Function} The defined wrapper class.\n * @private\n */\nfunction defineWrapper(BaseEvent, proto) {\n    const keys = Object.keys(proto)\n    if (keys.length === 0) {\n        return BaseEvent\n    }\n\n    /** CustomEvent */\n    function CustomEvent(eventTarget, event) {\n        BaseEvent.call(this, eventTarget, event)\n    }\n\n    CustomEvent.prototype = Object.create(BaseEvent.prototype, {\n        constructor: { value: CustomEvent, configurable: true, writable: true },\n    })\n\n    // Define accessors.\n    for (let i = 0; i < keys.length; ++i) {\n        const key = keys[i]\n        if (!(key in BaseEvent.prototype)) {\n            const descriptor = Object.getOwnPropertyDescriptor(proto, key)\n            const isFunc = typeof descriptor.value === \"function\"\n            Object.defineProperty(\n                CustomEvent.prototype,\n                key,\n                isFunc\n                    ? defineCallDescriptor(key)\n                    : defineRedirectDescriptor(key)\n            )\n        }\n    }\n\n    return CustomEvent\n}\n\n/**\n * Get the wrapper class of a given prototype.\n * @param {Object} proto The prototype of the original event to get its wrapper.\n * @returns {Function} The wrapper class.\n * @private\n */\nfunction getWrapper(proto) {\n    if (proto == null || proto === Object.prototype) {\n        return Event\n    }\n\n    let wrapper = wrappers.get(proto)\n    if (wrapper == null) {\n        wrapper = defineWrapper(getWrapper(Object.getPrototypeOf(proto)), proto)\n        wrappers.set(proto, wrapper)\n    }\n    return wrapper\n}\n\n/**\n * Wrap a given event to management a dispatching.\n * @param {EventTarget} eventTarget The event target of this dispatching.\n * @param {Object} event The event to wrap.\n * @returns {Event} The wrapper instance.\n * @private\n */\nexport function wrapEvent(eventTarget, event) {\n    const Wrapper = getWrapper(Object.getPrototypeOf(event))\n    return new Wrapper(eventTarget, event)\n}\n\n/**\n * Get the immediateStopped flag of a given event.\n * @param {Event} event The event to get.\n * @returns {boolean} The flag to stop propagation immediately.\n * @private\n */\nexport function isStopped(event) {\n    return pd(event).immediateStopped\n}\n\n/**\n * Set the current event phase of a given event.\n * @param {Event} event The event to set current target.\n * @param {number} eventPhase New event phase.\n * @returns {void}\n * @private\n */\nexport function setEventPhase(event, eventPhase) {\n    pd(event).eventPhase = eventPhase\n}\n\n/**\n * Set the current target of a given event.\n * @param {Event} event The event to set current target.\n * @param {EventTarget|null} currentTarget New current target.\n * @returns {void}\n * @private\n */\nexport function setCurrentTarget(event, currentTarget) {\n    pd(event).currentTarget = currentTarget\n}\n\n/**\n * Set a passive listener of a given event.\n * @param {Event} event The event to set current target.\n * @param {Function|null} passiveListener New passive listener.\n * @returns {void}\n * @private\n */\nexport function setPassiveListener(event, passiveListener) {\n    pd(event).passiveListener = passiveListener\n}\n", "import {\n    isStopped,\n    setCurrentTarget,\n    setEventPhase,\n    setPassiveListener,\n    wrapEvent,\n} from \"./event.mjs\"\n\n/**\n * @typedef {object} ListenerNode\n * @property {Function} listener\n * @property {1|2|3} listenerType\n * @property {boolean} passive\n * @property {boolean} once\n * @property {ListenerNode|null} next\n * @private\n */\n\n/**\n * @type {WeakMap<object, Map<string, ListenerNode>>}\n * @private\n */\nconst listenersMap = new WeakMap()\n\n// Listener types\nconst CAPTURE = 1\nconst BUBBLE = 2\nconst ATTRIBUTE = 3\n\n/**\n * Check whether a given value is an object or not.\n * @param {any} x The value to check.\n * @returns {boolean} `true` if the value is an object.\n */\nfunction isObject(x) {\n    return x !== null && typeof x === \"object\" //eslint-disable-line no-restricted-syntax\n}\n\n/**\n * Get listeners.\n * @param {EventTarget} eventTarget The event target to get.\n * @returns {Map<string, ListenerNode>} The listeners.\n * @private\n */\nfunction getListeners(eventTarget) {\n    const listeners = listenersMap.get(eventTarget)\n    if (listeners == null) {\n        throw new TypeError(\n            \"'this' is expected an EventTarget object, but got another value.\"\n        )\n    }\n    return listeners\n}\n\n/**\n * Get the property descriptor for the event attribute of a given event.\n * @param {string} eventName The event name to get property descriptor.\n * @returns {PropertyDescriptor} The property descriptor.\n * @private\n */\nfunction defineEventAttributeDescriptor(eventName) {\n    return {\n        get() {\n            const listeners = getListeners(this)\n            let node = listeners.get(eventName)\n            while (node != null) {\n                if (node.listenerType === ATTRIBUTE) {\n                    return node.listener\n                }\n                node = node.next\n            }\n            return null\n        },\n\n        set(listener) {\n            if (typeof listener !== \"function\" && !isObject(listener)) {\n                listener = null // eslint-disable-line no-param-reassign\n            }\n            const listeners = getListeners(this)\n\n            // Traverse to the tail while removing old value.\n            let prev = null\n            let node = listeners.get(eventName)\n            while (node != null) {\n                if (node.listenerType === ATTRIBUTE) {\n                    // Remove old value.\n                    if (prev !== null) {\n                        prev.next = node.next\n                    } else if (node.next !== null) {\n                        listeners.set(eventName, node.next)\n                    } else {\n                        listeners.delete(eventName)\n                    }\n                } else {\n                    prev = node\n                }\n\n                node = node.next\n            }\n\n            // Add new value.\n            if (listener !== null) {\n                const newNode = {\n                    listener,\n                    listenerType: ATTRIBUTE,\n                    passive: false,\n                    once: false,\n                    next: null,\n                }\n                if (prev === null) {\n                    listeners.set(eventName, newNode)\n                } else {\n                    prev.next = newNode\n                }\n            }\n        },\n        configurable: true,\n        enumerable: true,\n    }\n}\n\n/**\n * Define an event attribute (e.g. `eventTarget.onclick`).\n * @param {Object} eventTargetPrototype The event target prototype to define an event attrbite.\n * @param {string} eventName The event name to define.\n * @returns {void}\n */\nfunction defineEventAttribute(eventTargetPrototype, eventName) {\n    Object.defineProperty(\n        eventTargetPrototype,\n        `on${eventName}`,\n        defineEventAttributeDescriptor(eventName)\n    )\n}\n\n/**\n * Define a custom EventTarget with event attributes.\n * @param {string[]} eventNames Event names for event attributes.\n * @returns {EventTarget} The custom EventTarget.\n * @private\n */\nfunction defineCustomEventTarget(eventNames) {\n    /** CustomEventTarget */\n    function CustomEventTarget() {\n        EventTarget.call(this)\n    }\n\n    CustomEventTarget.prototype = Object.create(EventTarget.prototype, {\n        constructor: {\n            value: CustomEventTarget,\n            configurable: true,\n            writable: true,\n        },\n    })\n\n    for (let i = 0; i < eventNames.length; ++i) {\n        defineEventAttribute(CustomEventTarget.prototype, eventNames[i])\n    }\n\n    return CustomEventTarget\n}\n\n/**\n * EventTarget.\n *\n * - This is constructor if no arguments.\n * - This is a function which returns a CustomEventTarget constructor if there are arguments.\n *\n * For example:\n *\n *     class A extends EventTarget {}\n *     class B extends EventTarget(\"message\") {}\n *     class C extends EventTarget(\"message\", \"error\") {}\n *     class D extends EventTarget([\"message\", \"error\"]) {}\n */\nfunction EventTarget() {\n    /*eslint-disable consistent-return */\n    if (this instanceof EventTarget) {\n        listenersMap.set(this, new Map())\n        return\n    }\n    if (arguments.length === 1 && Array.isArray(arguments[0])) {\n        return defineCustomEventTarget(arguments[0])\n    }\n    if (arguments.length > 0) {\n        const types = new Array(arguments.length)\n        for (let i = 0; i < arguments.length; ++i) {\n            types[i] = arguments[i]\n        }\n        return defineCustomEventTarget(types)\n    }\n    throw new TypeError(\"Cannot call a class as a function\")\n    /*eslint-enable consistent-return */\n}\n\n// Should be enumerable, but class methods are not enumerable.\nEventTarget.prototype = {\n    /**\n     * Add a given listener to this event target.\n     * @param {string} eventName The event name to add.\n     * @param {Function} listener The listener to add.\n     * @param {boolean|{capture?:boolean,passive?:boolean,once?:boolean}} [options] The options for this listener.\n     * @returns {void}\n     */\n    addEventListener(eventName, listener, options) {\n        if (listener == null) {\n            return\n        }\n        if (typeof listener !== \"function\" && !isObject(listener)) {\n            throw new TypeError(\"'listener' should be a function or an object.\")\n        }\n\n        const listeners = getListeners(this)\n        const optionsIsObj = isObject(options)\n        const capture = optionsIsObj\n            ? Boolean(options.capture)\n            : Boolean(options)\n        const listenerType = capture ? CAPTURE : BUBBLE\n        const newNode = {\n            listener,\n            listenerType,\n            passive: optionsIsObj && Boolean(options.passive),\n            once: optionsIsObj && Boolean(options.once),\n            next: null,\n        }\n\n        // Set it as the first node if the first node is null.\n        let node = listeners.get(eventName)\n        if (node === undefined) {\n            listeners.set(eventName, newNode)\n            return\n        }\n\n        // Traverse to the tail while checking duplication..\n        let prev = null\n        while (node != null) {\n            if (\n                node.listener === listener &&\n                node.listenerType === listenerType\n            ) {\n                // Should ignore duplication.\n                return\n            }\n            prev = node\n            node = node.next\n        }\n\n        // Add it.\n        prev.next = newNode\n    },\n\n    /**\n     * Remove a given listener from this event target.\n     * @param {string} eventName The event name to remove.\n     * @param {Function} listener The listener to remove.\n     * @param {boolean|{capture?:boolean,passive?:boolean,once?:boolean}} [options] The options for this listener.\n     * @returns {void}\n     */\n    removeEventListener(eventName, listener, options) {\n        if (listener == null) {\n            return\n        }\n\n        const listeners = getListeners(this)\n        const capture = isObject(options)\n            ? Boolean(options.capture)\n            : Boolean(options)\n        const listenerType = capture ? CAPTURE : BUBBLE\n\n        let prev = null\n        let node = listeners.get(eventName)\n        while (node != null) {\n            if (\n                node.listener === listener &&\n                node.listenerType === listenerType\n            ) {\n                if (prev !== null) {\n                    prev.next = node.next\n                } else if (node.next !== null) {\n                    listeners.set(eventName, node.next)\n                } else {\n                    listeners.delete(eventName)\n                }\n                return\n            }\n\n            prev = node\n            node = node.next\n        }\n    },\n\n    /**\n     * Dispatch a given event.\n     * @param {Event|{type:string}} event The event to dispatch.\n     * @returns {boolean} `false` if canceled.\n     */\n    dispatchEvent(event) {\n        if (event == null || typeof event.type !== \"string\") {\n            throw new TypeError('\"event.type\" should be a string.')\n        }\n\n        // If listeners aren't registered, terminate.\n        const listeners = getListeners(this)\n        const eventName = event.type\n        let node = listeners.get(eventName)\n        if (node == null) {\n            return true\n        }\n\n        // Since we cannot rewrite several properties, so wrap object.\n        const wrappedEvent = wrapEvent(this, event)\n\n        // This doesn't process capturing phase and bubbling phase.\n        // This isn't participating in a tree.\n        let prev = null\n        while (node != null) {\n            // Remove this listener if it's once\n            if (node.once) {\n                if (prev !== null) {\n                    prev.next = node.next\n                } else if (node.next !== null) {\n                    listeners.set(eventName, node.next)\n                } else {\n                    listeners.delete(eventName)\n                }\n            } else {\n                prev = node\n            }\n\n            // Call this listener\n            setPassiveListener(\n                wrappedEvent,\n                node.passive ? node.listener : null\n            )\n            if (typeof node.listener === \"function\") {\n                try {\n                    node.listener.call(this, wrappedEvent)\n                } catch (err) {\n                    if (\n                        typeof console !== \"undefined\" &&\n                        typeof console.error === \"function\"\n                    ) {\n                        console.error(err)\n                    }\n                }\n            } else if (\n                node.listenerType !== ATTRIBUTE &&\n                typeof node.listener.handleEvent === \"function\"\n            ) {\n                node.listener.handleEvent(wrappedEvent)\n            }\n\n            // Break if `event.stopImmediatePropagation` was called.\n            if (isStopped(wrappedEvent)) {\n                break\n            }\n\n            node = node.next\n        }\n        setPassiveListener(wrappedEvent, null)\n        setEventPhase(wrappedEvent, 0)\n        setCurrentTarget(wrappedEvent, null)\n\n        return !wrappedEvent.defaultPrevented\n    },\n}\n\n// `constructor` is not enumerable.\nObject.defineProperty(EventTarget.prototype, \"constructor\", {\n    value: EventTarget,\n    configurable: true,\n    writable: true,\n})\n\n// Ensure `eventTarget instanceof window.EventTarget` is `true`.\nif (\n    typeof window !== \"undefined\" &&\n    typeof window.EventTarget !== \"undefined\"\n) {\n    Object.setPrototypeOf(EventTarget.prototype, window.EventTarget.prototype)\n}\n\nexport { defineEventAttribute, EventTarget }\nexport default EventTarget\n"], "names": ["pd", "event", "retv", "privateData", "get", "console", "assert", "setCancelFlag", "data", "passiveListener", "cancelable", "canceled", "preventDefault", "error", "Event", "eventTarget", "set", "eventPhase", "currentTarget", "stopped", "immediateStopped", "timeStamp", "Date", "now", "Object", "defineProperty", "value", "enumerable", "key", "keys", "i", "length", "defineRedirectDescriptor", "configurable", "defineCallDescriptor", "apply", "arguments", "defineWrapper", "BaseEvent", "proto", "CustomEvent", "call", "prototype", "create", "constructor", "writable", "descriptor", "getOwnPropertyDescriptor", "isFunc", "getWrapper", "wrapper", "wrappers", "getPrototypeOf", "wrapEvent", "Wrapper", "isStopped", "setEventPhase", "set<PERSON><PERSON><PERSON><PERSON>arget", "setPassiveListener", "isObject", "x", "_typeof", "getListeners", "listeners", "listenersMap", "TypeError", "defineEventAttributeDescriptor", "eventName", "node", "listenerType", "listener", "next", "prev", "delete", "newNode", "passive", "once", "defineEventAttribute", "eventTargetPrototype", "defineCustomEventTarget", "eventNames", "CustomEventTarget", "EventTarget", "Map", "Array", "isArray", "types", "WeakMap", "type", "target", "<PERSON><PERSON><PERSON>", "NONE", "CAPTURING_PHASE", "AT_TARGET", "BUBBLING_PHASE", "stopPropagation", "stopImmediatePropagation", "bubbles", "defaultPrevented", "composed", "srcElement", "cancelBubble", "returnValue", "initEvent", "window", "setPrototypeOf", "CAPTURE", "BUBBLE", "addEventListener", "options", "optionsIsObj", "capture", "removeEventListener", "dispatchEvent", "wrappedEvent", "err", "handleEvent"], "mappings": ";;;;wbAkCA,QAASA,CAAAA,CAAT,CAAYC,CAAZ,CAAmB,IACTC,CAAAA,CAAI,CAAGC,CAAW,CAACC,GAAZD,CAAgBF,CAAhBE,QACbE,CAAAA,OAAO,CAACC,MAARD,CACY,IAARH,EAAAA,CADJG,CAEI,6CAFJA,CAGIJ,CAHJI,EAKOH,EAOX,QAASK,CAAAA,CAAT,CAAuBC,CAAvB,CAA6B,OACG,KAAxBA,EAAAA,CAAI,CAACC,eADgB,MAarB,CAACD,CAAI,CAACP,KAALO,CAAWE,UAbS,GAiBzBF,CAAI,CAACG,QAALH,GAjByB,CAkBgB,UAArC,QAAOA,CAAAA,CAAI,CAACP,KAALO,CAAWI,cAlBG,EAmBrBJ,CAAI,CAACP,KAALO,CAAWI,cAAXJ,EAnBqB,QAGE,WAAnB,QAAOH,CAAAA,OAAP,EACyB,UAAzB,QAAOA,CAAAA,OAAO,CAACQ,KAJE,EAMjBR,OAAO,CAACQ,KAARR,CACI,oEADJA,CAEIG,CAAI,CAACC,eAFTJ,CANiB,EAiC7B,QAASS,CAAAA,CAAT,CAAeC,CAAf,CAA4Bd,CAA5B,CAAmC,CAC/BE,CAAW,CAACa,GAAZb,CAAgB,IAAhBA,CAAsB,CAClBY,WAAW,CAAXA,CADkB,CAElBd,KAAK,CAALA,CAFkB,CAGlBgB,UAAU,CAAE,CAHM,CAIlBC,aAAa,CAAEH,CAJG,CAKlBJ,QAAQ,GALU,CAMlBQ,OAAO,GANW,CAOlBC,gBAAgB,GAPE,CAQlBX,eAAe,CAAE,IARC,CASlBY,SAAS,CAAEpB,CAAK,CAACoB,SAANpB,EAAmBqB,IAAI,CAACC,GAALD,EATZ,CAAtBnB,CAD+B,CAc/BqB,MAAM,CAACC,cAAPD,CAAsB,IAAtBA,CAA4B,WAA5BA,CAAyC,CAAEE,KAAK,GAAP,CAAgBC,UAAU,GAA1B,CAAzCH,CAd+B,QAmBrBI,CAAAA,EAFJC,CAAI,CAAGL,MAAM,CAACK,IAAPL,CAAYvB,CAAZuB,EACJM,CAAC,CAAG,EAAGA,CAAC,CAAGD,CAAI,CAACE,OAAQ,EAAED,EACzBF,EAAMC,CAAI,CAACC,CAAD,EACVF,CAAG,GAAI,OACTJ,MAAM,CAACC,cAAPD,CAAsB,IAAtBA,CAA4BI,CAA5BJ,CAAiCQ,CAAwB,CAACJ,CAAD,CAAzDJ,EAyOZ,QAASQ,CAAAA,CAAT,CAAkCJ,CAAlC,CAAuC,OAC5B,CACHxB,GADG,WACG,OACKJ,CAAAA,CAAE,CAAC,IAAD,CAAFA,CAASC,KAATD,CAAe4B,CAAf5B,CAFR,CAAA,CAIHgB,GAJG,UAICU,EAAO,CACP1B,CAAE,CAAC,IAAD,CAAFA,CAASC,KAATD,CAAe4B,CAAf5B,EAAsB0B,CALvB,CAAA,CAOHO,YAAY,GAPT,CAQHN,UAAU,GARP,EAkBX,QAASO,CAAAA,CAAT,CAA8BN,CAA9B,CAAmC,OACxB,CACHF,KADG,WACK,IACEzB,CAAAA,CAAK,CAAGD,CAAE,CAAC,IAAD,CAAFA,CAASC,YAChBA,CAAAA,CAAK,CAAC2B,CAAD,CAAL3B,CAAWkC,KAAXlC,CAAiBA,CAAjBA,CAAwBmC,SAAxBnC,CAHR,CAAA,CAKHgC,YAAY,GALT,CAMHN,UAAU,GANP,EAiBX,QAASU,CAAAA,CAAT,CAAuBC,CAAvB,CAAkCC,CAAlC,CAAyC,SAO5BC,CAAAA,EAAYzB,EAAad,EAAO,CACrCqC,CAAS,CAACG,IAAVH,CAAe,IAAfA,CAAqBvB,CAArBuB,CAAkCrC,CAAlCqC,KAPET,CAAAA,CAAI,CAAGL,MAAM,CAACK,IAAPL,CAAYe,CAAZf,KACO,CAAhBK,GAAAA,CAAI,CAACE,aACEO,CAAAA,EAQXE,CAAW,CAACE,SAAZF,CAAwBhB,MAAM,CAACmB,MAAPnB,CAAcc,CAAS,CAACI,SAAxBlB,CAAmC,CACvDoB,WAAW,CAAE,CAAElB,KAAK,CAAEc,CAAT,CAAsBP,YAAY,GAAlC,CAA0CY,QAAQ,GAAlD,CAD0C,CAAnCrB,CAXa,KAgBhC,GACKI,CAAAA,CADL,CAAIE,CAAC,CAAG,EAAGA,CAAC,CAAGD,CAAI,CAACE,OAAQ,EAAED,KACzBF,EAAMC,CAAI,CAACC,CAAD,EACZ,EAAEF,CAAG,GAAIU,CAAAA,CAAS,CAACI,SAAnB,EAA+B,IACzBI,CAAAA,CAAU,CAAGtB,MAAM,CAACuB,wBAAPvB,CAAgCe,CAAhCf,CAAuCI,CAAvCJ,CADY,CAEzBwB,CAAM,CAA+B,UAA5B,QAAOF,CAAAA,CAAU,CAACpB,KAFF,CAG/BF,MAAM,CAACC,cAAPD,CACIgB,CAAW,CAACE,SADhBlB,CAEII,CAFJJ,CAGIwB,CAAM,CACAd,CAAoB,CAACN,CAAD,CADpB,CAEAI,CAAwB,CAACJ,CAAD,CALlCJ,QAUDgB,CAAAA,EASX,QAASS,CAAAA,CAAT,CAAoBV,CAApB,CAA2B,IACV,IAATA,EAAAA,CAAK,EAAYA,CAAK,GAAKf,MAAM,CAACkB,gBAC3B5B,CAAAA,KAGPoC,CAAAA,CAAO,CAAGC,CAAQ,CAAC/C,GAAT+C,CAAaZ,CAAbY,QACC,KAAXD,EAAAA,IACAA,CAAO,CAAGb,CAAa,CAACY,CAAU,CAACzB,MAAM,CAAC4B,cAAP5B,CAAsBe,CAAtBf,CAAD,CAAX,CAA2Ce,CAA3C,EACvBY,CAAQ,CAACnC,GAATmC,CAAaZ,CAAbY,CAAoBD,CAApBC,GAEGD,EAUJ,QAASG,CAAAA,CAAT,CAAmBtC,CAAnB,CAAgCd,CAAhC,CAAuC,IACpCqD,CAAAA,CAAO,CAAGL,CAAU,CAACzB,MAAM,CAAC4B,cAAP5B,CAAsBvB,CAAtBuB,CAAD,QACnB,IAAI8B,CAAAA,CAAJ,CAAYvC,CAAZ,CAAyBd,CAAzB,EASJ,QAASsD,CAAAA,CAAT,CAAmBtD,CAAnB,CAA0B,OACtBD,CAAAA,CAAE,CAACC,CAAD,CAAFD,CAAUoB,iBAUd,QAASoC,CAAAA,CAAT,CAAuBvD,CAAvB,CAA8BgB,CAA9B,CAA0C,CAC7CjB,CAAE,CAACC,CAAD,CAAFD,CAAUiB,UAAVjB,CAAuBiB,EAUpB,QAASwC,CAAAA,CAAT,CAA0BxD,CAA1B,CAAiCiB,CAAjC,CAAgD,CACnDlB,CAAE,CAACC,CAAD,CAAFD,CAAUkB,aAAVlB,CAA0BkB,EAUvB,QAASwC,CAAAA,CAAT,CAA4BzD,CAA5B,CAAmCQ,CAAnC,CAAoD,CACvDT,CAAE,CAACC,CAAD,CAAFD,CAAUS,eAAVT,CAA4BS,EC3bhC,QAASkD,CAAAA,CAAT,CAAkBC,CAAlB,CAAqB,OACJ,KAANA,GAAAA,CAAC,EAA0B,QAAb,GAAAC,EAAOD,GAShC,QAASE,CAAAA,CAAT,CAAsB/C,CAAtB,CAAmC,IACzBgD,CAAAA,CAAS,CAAGC,CAAY,CAAC5D,GAAb4D,CAAiBjD,CAAjBiD,KACD,IAAbD,EAAAA,OACM,IAAIE,CAAAA,SAAJ,CACF,kEADE,QAIHF,CAAAA,EASX,QAASG,CAAAA,CAAT,CAAwCC,CAAxC,CAAmD,OACxC,CACH/D,GADG,WACG,QACI2D,CAAAA,CAAS,CAAGD,CAAY,CAAC,IAAD,CAD5B,CAEEM,CAAI,CAAGL,CAAS,CAAC3D,GAAV2D,CAAcI,CAAdJ,CAFT,CAGa,IAARK,EAAAA,CAHL,EAGmB,IACbA,IAAAA,CAAI,CAACC,mBACED,CAAAA,CAAI,CAACE,SAEhBF,CAAI,CAAGA,CAAI,CAACG,WAET,KAVR,CAAA,CAaHvD,GAbG,UAaCsD,EAAU,CACc,UAApB,QAAOA,CAAAA,CAAP,EAAmCX,CAAQ,CAACW,CAAD,CADrC,GAENA,CAAQ,CAAG,IAFL,SAIJP,CAAAA,CAAS,CAAGD,CAAY,CAAC,IAAD,CAJpB,CAONU,CAAI,CAAG,IAPD,CAQNJ,CAAI,CAAGL,CAAS,CAAC3D,GAAV2D,CAAcI,CAAdJ,CARD,CASK,IAARK,EAAAA,CATG,EAUFA,IAAAA,CAAI,CAACC,YAVH,CAYW,IAATG,GAAAA,CAZF,CAcuB,IAAdJ,GAAAA,CAAI,CAACG,IAdd,CAiBER,CAAS,CAACU,MAAVV,CAAiBI,CAAjBJ,CAjBF,CAeEA,CAAS,CAAC/C,GAAV+C,CAAcI,CAAdJ,CAAyBK,CAAI,CAACG,IAA9BR,CAfF,CAaES,CAAI,CAACD,IAALC,CAAYJ,CAAI,CAACG,IAbnB,CAoBFC,CAAI,CAAGJ,CApBL,CAuBNA,CAAI,CAAGA,CAAI,CAACG,IAvBN,IA2BO,IAAbD,GAAAA,EAAmB,IACbI,CAAAA,CAAO,CAAG,CACZJ,QAAQ,CAARA,CADY,CAEZD,YAAY,EAFA,CAGZM,OAAO,GAHK,CAIZC,IAAI,GAJQ,CAKZL,IAAI,CAAE,IALM,EAOH,IAATC,GAAAA,CARe,CASfT,CAAS,CAAC/C,GAAV+C,CAAcI,CAAdJ,CAAyBW,CAAzBX,CATe,CAWfS,CAAI,CAACD,IAALC,CAAYE,EAnDrB,CAAA,CAuDHzC,YAAY,GAvDT,CAwDHN,UAAU,GAxDP,EAkEX,QAASkD,CAAAA,CAAT,CAA8BC,CAA9B,CAAoDX,CAApD,CAA+D,CAC3D3C,MAAM,CAACC,cAAPD,CACIsD,CADJtD,aAES2C,EAFT3C,CAGI0C,CAA8B,CAACC,CAAD,CAHlC3C,EAaJ,QAASuD,CAAAA,CAAT,CAAiCC,CAAjC,CAA6C,SAEhCC,CAAAA,GAAoB,CACzBC,CAAW,CAACzC,IAAZyC,CAAiB,IAAjBA,EAGJD,CAAiB,CAACvC,SAAlBuC,CAA8BzD,MAAM,CAACmB,MAAPnB,CAAc0D,CAAW,CAACxC,SAA1BlB,CAAqC,CAC/DoB,WAAW,CAAE,CACTlB,KAAK,CAAEuD,CADE,CAEThD,YAAY,GAFH,CAGTY,QAAQ,GAHC,CADkD,CAArCrB,CANW,KAcpC,GAAIM,CAAAA,CAAC,CAAG,EAAGA,CAAC,CAAGkD,CAAU,CAACjD,OAAQ,EAAED,EACrC+C,CAAoB,CAACI,CAAiB,CAACvC,SAAnB,CAA8BsC,CAAU,CAAClD,CAAD,CAAxC,CAApB+C,OAGGI,CAAAA,EAgBX,QAASC,CAAAA,CAAT,EAAuB,IAEf,eAAgBA,CAAAA,aAChBlB,CAAAA,CAAY,CAAChD,GAAbgD,CAAiB,IAAjBA,CAAuB,GAAImB,CAAAA,GAA3BnB,KAGqB,CAArB5B,GAAAA,SAAS,CAACL,MAAVK,EAA0BgD,KAAK,CAACC,OAAND,CAAchD,SAAS,CAAC,CAAD,CAAvBgD,QACnBL,CAAAA,CAAuB,CAAC3C,SAAS,CAAC,CAAD,CAAV,KAEX,CAAnBA,CAAAA,SAAS,CAACL,OAAY,QAChBuD,CAAAA,CAAK,CAAOF,KAAP,CAAahD,SAAS,CAACL,MAAvB,EACFD,CAAC,CAAG,EAAGA,CAAC,CAAGM,SAAS,CAACL,OAAQ,EAAED,EACpCwD,CAAK,CAACxD,CAAD,CAALwD,CAAWlD,SAAS,CAACN,CAAD,CAApBwD,OAEGP,CAAAA,CAAuB,CAACO,CAAD,OAE5B,IAAIrB,CAAAA,SAAJ,CAAc,mCAAd,KD5KJ9D,CAAAA,CAAW,CAAG,GAAIoF,CAAAA,QAOlBpC,CAAQ,CAAG,GAAIoC,CAAAA,QAkFrBzE,CAAK,CAAC4B,SAAN5B,CAAkB,IAKV0E,CAAAA,MAAO,OACAxF,CAAAA,CAAE,CAAC,IAAD,CAAFA,CAASC,KAATD,CAAewF,IANZ,CAAA,IAaVC,CAAAA,QAAS,OACFzF,CAAAA,CAAE,CAAC,IAAD,CAAFA,CAASe,WAdN,CAAA,IAqBVG,CAAAA,eAAgB,OACTlB,CAAAA,CAAE,CAAC,IAAD,CAAFA,CAASkB,aAtBN,CAAA,CA4BdwE,YA5Bc,WA4BC,IACLxE,CAAAA,CAAa,CAAGlB,CAAE,CAAC,IAAD,CAAFA,CAASkB,cADpB,MAEU,KAAjBA,EAAAA,CAFO,CAGA,EAHA,CAKJ,CAACA,CAAD,CAjCG,CAAA,IAwCVyE,CAAAA,MAAO,OACA,EAzCG,CAAA,IAgDVC,CAAAA,iBAAkB,OACX,EAjDG,CAAA,IAwDVC,CAAAA,WAAY,OACL,EAzDG,CAAA,IAgEVC,CAAAA,gBAAiB,OACV,EAjEG,CAAA,IAwEV7E,CAAAA,YAAa,OACNjB,CAAAA,CAAE,CAAC,IAAD,CAAFA,CAASiB,UAzEN,CAAA,CAgFd8E,eAhFc,WAgFI,IACRvF,CAAAA,CAAI,CAAGR,CAAE,CAAC,IAAD,EAEfQ,CAAI,CAACW,OAALX,GAHc,CAI4B,UAAtC,QAAOA,CAAAA,CAAI,CAACP,KAALO,CAAWuF,eAJR,EAKVvF,CAAI,CAACP,KAALO,CAAWuF,eAAXvF,EArFM,CAAA,CA6FdwF,wBA7Fc,WA6Fa,IACjBxF,CAAAA,CAAI,CAAGR,CAAE,CAAC,IAAD,EAEfQ,CAAI,CAACW,OAALX,GAHuB,CAIvBA,CAAI,CAACY,gBAALZ,GAJuB,CAK4B,UAA/C,QAAOA,CAAAA,CAAI,CAACP,KAALO,CAAWwF,wBALC,EAMnBxF,CAAI,CAACP,KAALO,CAAWwF,wBAAXxF,EAnGM,CAAA,IA2GVyF,CAAAA,SAAU,SACKjG,CAAE,CAAC,IAAD,CAAFA,CAASC,KAATD,CAAeiG,OA5GpB,CAAA,IAmHVvF,CAAAA,YAAa,SACEV,CAAE,CAAC,IAAD,CAAFA,CAASC,KAATD,CAAeU,UApHpB,CAAA,CA2HdE,cA3Hc,WA2HG,CACbL,CAAa,CAACP,CAAE,CAAC,IAAD,CAAH,CA5HH,CAAA,IAmIVkG,CAAAA,kBAAmB,OACZlG,CAAAA,CAAE,CAAC,IAAD,CAAFA,CAASW,QApIN,CAAA,IA2IVwF,CAAAA,UAAW,SACInG,CAAE,CAAC,IAAD,CAAFA,CAASC,KAATD,CAAemG,QA5IpB,CAAA,IAmJV9E,CAAAA,WAAY,OACLrB,CAAAA,CAAE,CAAC,IAAD,CAAFA,CAASqB,SApJN,CAAA,IA4JV+E,CAAAA,YAAa,OACNpG,CAAAA,CAAE,CAAC,IAAD,CAAFA,CAASe,WA7JN,CAAA,IAqKVsF,CAAAA,cAAe,OACRrG,CAAAA,CAAE,CAAC,IAAD,CAAFA,CAASmB,OAtKN,CAAA,IAwKVkF,CAAAA,aAAa3E,EAAO,IACfA,MAGClB,CAAAA,CAAI,CAAGR,CAAE,CAAC,IAAD,EAEfQ,CAAI,CAACW,OAALX,IACuC,SAAnC,QAAOA,CAAAA,CAAI,CAACP,KAALO,CAAW6F,eAClB7F,CAAI,CAACP,KAALO,CAAW6F,YAAX7F,KAhLM,CAAA,IAyLV8F,CAAAA,aAAc,OACP,CAACtG,CAAE,CAAC,IAAD,CAAFA,CAASW,QA1LP,CAAA,IA4LV2F,CAAAA,YAAY5E,EAAO,CACdA,CADc,EAEfnB,CAAa,CAACP,CAAE,CAAC,IAAD,CAAH,CA9LP,CAAA,CAyMduG,SAzMc,WAyMF,EAzME,EA+MlB/E,MAAM,CAACC,cAAPD,CAAsBV,CAAK,CAAC4B,SAA5BlB,CAAuC,aAAvCA,CAAsD,CAClDE,KAAK,CAAEZ,CAD2C,CAElDmB,YAAY,GAFsC,CAGlDY,QAAQ,GAH0C,CAAtDrB,EAOsB,WAAlB,QAAOgF,CAAAA,MAAP,EAAyD,WAAxB,QAAOA,CAAAA,MAAM,CAAC1F,QAC/CU,MAAM,CAACiF,cAAPjF,CAAsBV,CAAK,CAAC4B,SAA5BlB,CAAuCgF,MAAM,CAAC1F,KAAP0F,CAAa9D,SAApDlB,EAGA2B,CAAQ,CAACnC,GAATmC,CAAaqD,MAAM,CAAC1F,KAAP0F,CAAa9D,SAA1BS,CAAqCrC,CAArCqC,MChTEa,CAAAA,CAAY,CAAG,GAAIuB,CAAAA,QAGnBmB,CAAO,CAAG,EACVC,CAAM,CAAG,KA0KfzB,CAAW,CAACxC,SAAZwC,CAAwB,CAQpB0B,gBARoB,UAQHzC,EAAWG,EAAUuC,EAAS,IAC3B,IAAZvC,EAAAA,MAGoB,UAApB,QAAOA,CAAAA,CAAP,EAAkC,CAACX,CAAQ,CAACW,CAAD,OACrC,IAAIL,CAAAA,SAAJ,CAAc,+CAAd,KAGJF,CAAAA,CAAS,CAAGD,CAAY,CAAC,IAAD,EACxBgD,CAAY,CAAGnD,CAAQ,CAACkD,CAAD,EACvBE,CAAO,CAAGD,CAAY,GACdD,CAAO,CAACE,OADM,GAEdF,EACRxC,CAAY,CAAG0C,CAAO,CAAGL,CAAH,CAAaC,EACnCjC,CAAO,CAAG,CACZJ,QAAQ,CAARA,CADY,CAEZD,YAAY,CAAZA,CAFY,CAGZM,OAAO,CAAEmC,CAAY,IAAYD,CAAO,CAAClC,OAH7B,CAIZC,IAAI,CAAEkC,CAAY,IAAYD,CAAO,CAACjC,IAJ1B,CAKZL,IAAI,CAAE,IALM,EASZH,CAAI,CAAGL,CAAS,CAAC3D,GAAV2D,CAAcI,CAAdJ,KACPK,SAAAA,aACAL,CAAAA,CAAS,CAAC/C,GAAV+C,CAAcI,CAAdJ,CAAyBW,CAAzBX,SAKAS,CAAAA,CAAI,CAAG,KACI,IAARJ,EAAAA,GAAc,IAEbA,CAAI,CAACE,QAALF,GAAkBE,CAAlBF,EACAA,CAAI,CAACC,YAALD,GAAsBC,SAK1BG,CAAI,CAAGJ,CARU,CASjBA,CAAI,CAAGA,CAAI,CAACG,IAxC2B,CA4C3CC,CAAI,CAACD,IAALC,CAAYE,EApDI,CAAA,CA8DpBsC,mBA9DoB,UA8DA7C,EAAWG,EAAUuC,EAAS,IAC9B,IAAZvC,EAAAA,SAIEP,CAAAA,CAAS,CAAGD,CAAY,CAAC,IAAD,EACxBiD,CAAO,CAAGpD,CAAQ,CAACkD,CAAD,CAARlD,GACFkD,CAAO,CAACE,OADNpD,GAEFkD,EACRxC,CAAY,CAAG0C,CAAO,CAAGL,CAAH,CAAaC,EAErCnC,CAAI,CAAG,KACPJ,CAAI,CAAGL,CAAS,CAAC3D,GAAV2D,CAAcI,CAAdJ,EACI,IAARK,EAAAA,GAAc,IAEbA,CAAI,CAACE,QAALF,GAAkBE,CAAlBF,EACAA,CAAI,CAACC,YAALD,GAAsBC,cAET,IAATG,GAAAA,EAEqB,IAAdJ,GAAAA,CAAI,CAACG,KAGZR,CAAS,CAACU,MAAVV,CAAiBI,CAAjBJ,EAFAA,CAAS,CAAC/C,GAAV+C,CAAcI,CAAdJ,CAAyBK,CAAI,CAACG,IAA9BR,EAFAS,CAAI,CAACD,IAALC,CAAYJ,CAAI,CAACG,MASzBC,CAAI,CAAGJ,CAfU,CAgBjBA,CAAI,CAAGA,CAAI,CAACG,KA3FA,CAAA,CAoGpB0C,aApGoB,UAoGNhH,EAAO,IACJ,IAATA,EAAAA,CAAK,EAAkC,QAAtB,QAAOA,CAAAA,CAAK,CAACuF,UACxB,IAAIvB,CAAAA,SAAJ,CAAc,oCAAd,EAFO,GAMXF,CAAAA,CAAS,CAAGD,CAAY,CAAC,IAAD,CANb,CAOXK,CAAS,CAAGlE,CAAK,CAACuF,IAPP,CAQbpB,CAAI,CAAGL,CAAS,CAAC3D,GAAV2D,CAAcI,CAAdJ,CARM,IASL,IAARK,EAAAA,WATa,OAcX8C,CAAAA,CAAY,CAAG7D,CAAS,CAAC,IAAD,CAAOpD,CAAP,CAdb,CAkBbuE,CAAI,CAAG,IAlBM,CAmBF,IAARJ,EAAAA,CAnBU,EAmBI,IAEbA,CAAI,CAACQ,KACQ,IAATJ,GAAAA,EAEqB,IAAdJ,GAAAA,CAAI,CAACG,KAGZR,CAAS,CAACU,MAAVV,CAAiBI,CAAjBJ,EAFAA,CAAS,CAAC/C,GAAV+C,CAAcI,CAAdJ,CAAyBK,CAAI,CAACG,IAA9BR,EAFAS,CAAI,CAACD,IAALC,CAAYJ,CAAI,CAACG,KAOrBC,CAAI,CAAGJ,EAIXV,CAAkB,CACdwD,CADc,CAEd9C,CAAI,CAACO,OAALP,CAAeA,CAAI,CAACE,QAApBF,CAA+B,IAFjB,EAIW,UAAzB,QAAOA,CAAAA,CAAI,CAACE,YACR,CACAF,CAAI,CAACE,QAALF,CAAc3B,IAAd2B,CAAmB,IAAnBA,CAAyB8C,CAAzB9C,CADJ,CAEE,MAAO+C,CAAP,CAAY,CAEa,WAAnB,QAAO9G,CAAAA,OAAP,EACyB,UAAzB,QAAOA,CAAAA,OAAO,CAACQ,KAHT,EAKNR,OAAO,CAACQ,KAARR,CAAc8G,CAAd9G,MAIR+D,CAAAA,CAAI,CAACC,YAALD,GA/TE,CA+TFA,EACqC,UAArC,QAAOA,CAAAA,CAAI,CAACE,QAALF,CAAcgD,aAErBhD,CAAI,CAACE,QAALF,CAAcgD,WAAdhD,CAA0B8C,CAA1B9C,KAIAb,CAAS,CAAC2D,CAAD,QAIb9C,CAAI,CAAGA,CAAI,CAACG,WAEhBb,CAAAA,CAAkB,CAACwD,CAAD,CAAe,IAAf,EAClB1D,CAAa,CAAC0D,CAAD,CAAe,CAAf,EACbzD,CAAgB,CAACyD,CAAD,CAAe,IAAf,EAET,CAACA,CAAY,CAAChB,iBAvKL,EA4KxB1E,MAAM,CAACC,cAAPD,CAAsB0D,CAAW,CAACxC,SAAlClB,CAA6C,aAA7CA,CAA4D,CACxDE,KAAK,CAAEwD,CADiD,CAExDjD,YAAY,GAF4C,CAGxDY,QAAQ,GAHgD,CAA5DrB,EAQsB,WAAlB,QAAOgF,CAAAA,MAAP,EAC8B,WAA9B,QAAOA,CAAAA,MAAM,CAACtB,aAEd1D,MAAM,CAACiF,cAAPjF,CAAsB0D,CAAW,CAACxC,SAAlClB,CAA6CgF,MAAM,CAACtB,WAAPsB,CAAmB9D,SAAhElB"}